'use client'

import { useState } from 'react'
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>, Card<PERSON><PERSON>nt, CardHeader, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { Heart, Brain, Smile, ArrowRight, Play, Pause, Coffee, Shield } from 'lucide-react'
import { 
  PSYCHOLOGICAL_ASSESSMENT_QUESTIONS, 
  analyzePsychologicalProfile,
  PsychologicalProfile 
} from '@/lib/psychological-service'

interface PsychologicalAssessmentProps {
  onComplete: (profile: PsychologicalProfile) => void
  onSkip: () => void
}

export default function PsychologicalAssessment({ onComplete, onSkip }: PsychologicalAssessmentProps) {
  const [currentStep, setCurrentStep] = useState<'intro' | 'breathing' | 'assessment' | 'warmup'>('intro')
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0)
  const [answers, setAnswers] = useState<Record<string, string>>({})
  const [isBreathingActive, setIsBreathingActive] = useState(false)
  const [breathingPhase, setBreathingPhase] = useState<'inhale' | 'hold' | 'exhale'>('inhale')
  const [breathingCount, setBreatheingCount] = useState(0)

  // Breathing exercise component
  const BreathingExercise = () => {
    const startBreathing = () => {
      setIsBreathingActive(true)
      let count = 0
      let phase: 'inhale' | 'hold' | 'exhale' = 'inhale'
      
      const breathingCycle = setInterval(() => {
        count++
        setBreatheingCount(count)
        
        if (count <= 4) {
          setBreathingPhase('inhale')
        } else if (count <= 7) {
          setBreathingPhase('hold')
        } else if (count <= 11) {
          setBreathingPhase('exhale')
        } else {
          count = 0
          setBreatheingCount(0)
        }
        
        if (count >= 44) { // 4 cycles of 11 seconds each
          clearInterval(breathingCycle)
          setIsBreathingActive(false)
          setTimeout(() => setCurrentStep('assessment'), 1000)
        }
      }, 1000)
    }

    const getBreathingInstruction = () => {
      switch (breathingPhase) {
        case 'inhale': return 'Tarik napas dalam-dalam...'
        case 'hold': return 'Tahan napas...'
        case 'exhale': return 'Hembuskan perlahan...'
      }
    }

    const getBreathingColor = () => {
      switch (breathingPhase) {
        case 'inhale': return 'from-blue-400 to-blue-600'
        case 'hold': return 'from-yellow-400 to-yellow-600'
        case 'exhale': return 'from-green-400 to-green-600'
      }
    }

    return (
      <div className="text-center space-y-8">
        <div className="space-y-4">
          <Heart className="h-16 w-16 mx-auto text-red-400" />
          <h2 className="text-2xl font-bold text-gray-900">Latihan Pernapasan</h2>
          <p className="text-gray-600 max-w-md mx-auto">
            Mari kita mulai dengan latihan pernapasan untuk membantu Anda merasa lebih tenang dan fokus.
          </p>
        </div>

        {!isBreathingActive ? (
          <div className="space-y-6">
            <div className="bg-blue-50 p-6 rounded-lg">
              <h3 className="font-semibold mb-2">Teknik 4-7-8 Breathing:</h3>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• Tarik napas selama 4 detik</li>
                <li>• Tahan napas selama 7 detik</li>
                <li>• Hembuskan napas selama 8 detik</li>
                <li>• Ulangi 4 kali</li>
              </ul>
            </div>
            <div className="flex gap-4 justify-center">
              <Button
                variant="outline"
                onClick={() => setCurrentStep('assessment')}
                size="lg"
                className="border-gray-300 text-gray-600 hover:bg-gray-50"
              >
                Lewati Latihan
              </Button>
              <Button
                onClick={startBreathing}
                size="lg"
                className="bg-gradient-to-r from-blue-500 to-indigo-500 hover:from-blue-600 hover:to-indigo-600"
              >
                <Play className="h-5 w-5 mr-2" />
                Mulai Latihan Pernapasan
              </Button>
            </div>
          </div>
        ) : (
          <div className="space-y-8">
            <div className={`w-32 h-32 mx-auto rounded-full bg-gradient-to-br ${getBreathingColor()} flex items-center justify-center transition-all duration-1000 ${breathingPhase === 'inhale' ? 'scale-110' : breathingPhase === 'exhale' ? 'scale-90' : 'scale-100'}`}>
              <div className="text-white font-bold text-lg">
                {breathingCount % 12 || 12}
              </div>
            </div>
            <div className="space-y-2">
              <p className="text-xl font-medium text-gray-800">{getBreathingInstruction()}</p>
              <p className="text-sm text-gray-500">Siklus {Math.floor(breathingCount / 12) + 1} dari 4</p>
            </div>
            <Progress value={(breathingCount / 44) * 100} className="w-64 mx-auto" />
          </div>
        )}
      </div>
    )
  }

  // Assessment questions component
  const AssessmentQuestions = () => {
    const currentQuestion = PSYCHOLOGICAL_ASSESSMENT_QUESTIONS[currentQuestionIndex]
    const progress = ((currentQuestionIndex + 1) / PSYCHOLOGICAL_ASSESSMENT_QUESTIONS.length) * 100

    const handleAnswer = (value: string) => {
      const newAnswers = { ...answers, [currentQuestion.id]: value }
      setAnswers(newAnswers)

      if (currentQuestionIndex < PSYCHOLOGICAL_ASSESSMENT_QUESTIONS.length - 1) {
        setCurrentQuestionIndex(currentQuestionIndex + 1)
      } else {
        // Complete assessment
        const profile = analyzePsychologicalProfile(newAnswers)
        setCurrentStep('warmup')
        setTimeout(() => onComplete(profile), 3000)
      }
    }

    return (
      <div className="space-y-8">
        <div className="text-center space-y-4">
          <Brain className="h-12 w-12 mx-auto text-purple-500" />
          <h2 className="text-2xl font-bold text-gray-900">Penilaian Psikologis Singkat</h2>
          <p className="text-gray-600">
            Beberapa pertanyaan untuk membantu kami menyesuaikan gaya wawancara dengan preferensi Anda
          </p>
          <Progress value={progress} className="w-full max-w-md mx-auto" />
          <p className="text-sm text-gray-500">
            Pertanyaan {currentQuestionIndex + 1} dari {PSYCHOLOGICAL_ASSESSMENT_QUESTIONS.length}
          </p>
        </div>

        <Card className="max-w-2xl mx-auto">
          <CardHeader>
            <CardTitle className="text-lg">{currentQuestion.question}</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            {currentQuestion.options.map((option) => (
              <Button
                key={option.value}
                variant="outline"
                className="w-full text-left justify-start h-auto p-4 hover:bg-blue-50 hover:border-blue-300"
                onClick={() => handleAnswer(option.value)}
              >
                <div className="text-sm">
                  {option.label}
                </div>
              </Button>
            ))}

            <div className="pt-4 border-t">
              <Button
                variant="ghost"
                className="w-full text-gray-500 hover:text-gray-700"
                onClick={() => {
                  // Skip assessment with default profile
                  const defaultProfile = analyzePsychologicalProfile({
                    anxiety_level: 'moderate',
                    communication_preference: 'gentle',
                    personality_type: 'ambivert',
                    interview_experience: 'some',
                    language_confidence: 'medium'
                  })
                  setCurrentStep('warmup')
                  setTimeout(() => onComplete(defaultProfile), 2000)
                }}
              >
                Lewati Penilaian (Gunakan Pengaturan Default)
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  // Warm-up completion component
  const WarmupComplete = () => (
    <div className="text-center space-y-8">
      <div className="space-y-4">
        <Smile className="h-16 w-16 mx-auto text-green-500" />
        <h2 className="text-2xl font-bold text-gray-900">Persiapan Selesai!</h2>
        <p className="text-gray-600 max-w-md mx-auto">
          Terima kasih! Kami telah menyesuaikan gaya wawancara berdasarkan preferensi Anda. 
          Anda siap untuk memulai wawancara yang nyaman dan personal.
        </p>
      </div>
      <div className="bg-green-50 p-6 rounded-lg max-w-md mx-auto">
        <h3 className="font-semibold text-green-800 mb-2">Yang Perlu Diingat:</h3>
        <ul className="text-sm text-green-700 space-y-1 text-left">
          <li>• Tidak ada jawaban yang salah</li>
          <li>• Berbicaralah dengan natural</li>
          <li>• Ambil waktu untuk berpikir jika perlu</li>
          <li>• Kami akan menyesuaikan dengan pace Anda</li>
        </ul>
      </div>
    </div>
  )

  // Main render
  if (currentStep === 'intro') {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 flex items-center justify-center p-4">
        <Card className="w-full max-w-2xl">
          <CardContent className="p-8 text-center space-y-8">
            <div className="space-y-4">
              <div className="w-20 h-20 mx-auto bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center">
                <Heart className="h-10 w-10 text-white" />
              </div>
              <h1 className="text-3xl font-bold text-gray-900">Persiapan Psikologis</h1>
              <p className="text-gray-600 leading-relaxed">
                Sebelum memulai wawancara, mari kita lakukan persiapan singkat untuk memastikan 
                Anda merasa nyaman dan dapat menampilkan performa terbaik.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
              <div className="bg-blue-50 p-4 rounded-lg">
                <Heart className="h-8 w-8 mx-auto mb-2 text-blue-500" />
                <h3 className="font-semibold mb-1">Latihan Pernapasan</h3>
                <p className="text-gray-600">Mengurangi kecemasan dan meningkatkan fokus</p>
              </div>
              <div className="bg-purple-50 p-4 rounded-lg">
                <Brain className="h-8 w-8 mx-auto mb-2 text-purple-500" />
                <h3 className="font-semibold mb-1">Penilaian Singkat</h3>
                <p className="text-gray-600">Menyesuaikan gaya komunikasi dengan preferensi Anda</p>
              </div>
              <div className="bg-green-50 p-4 rounded-lg">
                <Smile className="h-8 w-8 mx-auto mb-2 text-green-500" />
                <h3 className="font-semibold mb-1">Persiapan Mental</h3>
                <p className="text-gray-600">Membangun kepercayaan diri sebelum wawancara</p>
              </div>
            </div>

            <div className="flex gap-4 justify-center">
              <Button
                variant="outline"
                onClick={onSkip}
                size="lg"
                className="border-gray-300 text-gray-600 hover:bg-gray-50"
              >
                Lewati Persiapan
              </Button>
              <Button
                onClick={() => setCurrentStep('breathing')}
                size="lg"
                className="bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600"
              >
                Mulai Persiapan
                <ArrowRight className="h-5 w-5 ml-2" />
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 flex items-center justify-center p-4">
      <Card className="w-full max-w-3xl">
        <CardContent className="p-8">
          {currentStep === 'breathing' && <BreathingExercise />}
          {currentStep === 'assessment' && <AssessmentQuestions />}
          {currentStep === 'warmup' && <WarmupComplete />}
        </CardContent>
      </Card>
    </div>
  )
}
