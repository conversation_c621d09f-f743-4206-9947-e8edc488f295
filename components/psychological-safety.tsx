'use client'

import { useState, useEffect } from 'react'
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { Textarea } from "@/components/ui/textarea"
import { Coffee, Shield } from 'lucide-react'
import { PsychologicalProfile } from '@/lib/psychological-service'

interface PsychologicalSafetyProps {
  isVisible: boolean
  onClose: () => void
  onBreakComplete: () => void
  profile?: PsychologicalProfile
  stressLevel?: 'low' | 'medium' | 'high'
}

type PrePostTestData = {
  feeling: string
  score: number
}

type BreathingPhase = 'pre-test' | 'instructions' | 'breathing' | 'post-test' | 'completed'

export default function PsychologicalSafety({ 
  isVisible, 
  onClose, 
  onBreakComplete, 
  profile,
  stressLevel = 'medium'
}: PsychologicalSafetyProps) {
  const [breakType, setBreakType] = useState<'breathing' | 'affirmation' | 'rest'>('breathing')
  const [breakTimer, setBreakTimer] = useState(0)
  const [isBreakActive, setIsBreakActive] = useState(false)
  const [isBreakCompleted, setIsBreakCompleted] = useState(false)
  const [breathingPhase, setBreathingPhase] = useState<'inhale' | 'hold' | 'exhale'>('inhale')

  // Pre-post test states
  const [currentPhase, setCurrentPhase] = useState<BreathingPhase>('pre-test')
  const [preTestData, setPreTestData] = useState<PrePostTestData>({ feeling: '', score: 5 })
  const [postTestData, setPostTestData] = useState<PrePostTestData>({ feeling: '', score: 5 })

  // Auto-start break when component becomes visible
  useEffect(() => {
    if (isVisible && currentPhase === 'breathing' && !isBreakActive) {
      startBreak()
    }
  }, [isVisible, currentPhase])

  const startBreak = () => {
    setIsBreakActive(true)
    setBreakTimer(0)

    // Choose break type based on stress level and profile
    if (stressLevel === 'high' || (profile && profile.anxietyLevel === 'high')) {
      setBreakType('breathing')
    } else if (profile && profile.needsEncouragement) {
      setBreakType('affirmation')
    } else {
      setBreakType('rest')
    }
  }

  // Pre-test component
  const PreTestComponent = () => (
    <div className="text-center space-y-6">
      <div className="w-32 h-32 mx-auto rounded-full bg-gradient-to-br from-blue-400 to-blue-600 flex items-center justify-center">
        <div className="text-white text-4xl">📝</div>
      </div>
      <div className="space-y-4">
        <h3 className="text-2xl font-semibold text-gray-800">Sebelum Latihan Pernapasan</h3>
        <p className="text-gray-600">Mari kita ukur perasaan Anda saat ini</p>

        <div className="bg-gray-50 p-6 rounded-lg space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Bagaimana perasaan Anda saat ini?
            </label>
            <Textarea
              value={preTestData.feeling}
              onChange={(e) => setPreTestData(prev => ({ ...prev, feeling: e.target.value }))}
              placeholder="Contoh: Saya merasa cemas dan gugup..."
              className="resize-none h-20"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Seberapa tenang perasaan Anda? (1 = Sangat Cemas, 10 = Sangat Tenang)
            </label>
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-500">1</span>
              <input
                type="range"
                min="1"
                max="10"
                value={preTestData.score}
                onChange={(e) => setPreTestData(prev => ({ ...prev, score: parseInt(e.target.value) }))}
                className="flex-1"
              />
              <span className="text-sm text-gray-500">10</span>
            </div>
            <div className="text-center mt-2">
              <span className="text-2xl font-bold text-blue-600">{preTestData.score}</span>
            </div>
          </div>
        </div>

        <Button
          onClick={() => setCurrentPhase('instructions')}
          disabled={!preTestData.feeling.trim()}
          className="bg-gradient-to-r from-blue-500 to-indigo-500 hover:from-blue-600 hover:to-indigo-600"
        >
          Lanjutkan ke Instruksi
        </Button>
      </div>
    </div>
  )

  // Instructions component with visual grid
  const InstructionsComponent = () => (
    <div className="text-center space-y-6">
      <div className="w-32 h-32 mx-auto rounded-full bg-gradient-to-br from-green-400 to-emerald-600 flex items-center justify-center">
        <div className="text-white text-4xl">📋</div>
      </div>
      <div className="space-y-4">
        <h3 className="text-2xl font-semibold text-gray-800">Instruksi Latihan Pernapasan</h3>
        <p className="text-gray-600">Ikuti pola pernapasan berikut untuk merasa lebih tenang</p>

        <div className="bg-gray-50 p-6 rounded-lg space-y-4">
          <div className="text-sm text-gray-600 mb-4">
            <strong>Pola Pernapasan 4-3-4:</strong>
          </div>

          {/* Visual instruction grid */}
          <div className="grid grid-cols-4 gap-2 w-48 mx-auto mb-4">
            {Array.from({ length: 12 }, (_, i) => {
              let squareColor = 'bg-gray-200'
              let label = ''

              if (i < 4) {
                squareColor = 'bg-blue-500'
                label = 'T'
              } else if (i < 7) {
                squareColor = 'bg-yellow-500'
                label = 'H'
              } else if (i < 11) {
                squareColor = 'bg-green-500'
                label = 'B'
              } else {
                squareColor = 'bg-gray-300'
                label = '↻'
              }

              return (
                <div
                  key={i}
                  className={`w-10 h-10 ${squareColor} rounded flex items-center justify-center text-white font-bold text-sm`}
                >
                  {label}
                </div>
              )
            })}
          </div>

          <div className="space-y-2 text-sm">
            <div className="flex items-center justify-center space-x-2">
              <div className="w-4 h-4 bg-blue-500 rounded"></div>
              <span><strong>T (Tarik):</strong> Tarik napas dalam-dalam selama 4 detik</span>
            </div>
            <div className="flex items-center justify-center space-x-2">
              <div className="w-4 h-4 bg-yellow-500 rounded"></div>
              <span><strong>H (Tahan):</strong> Tahan napas selama 3 detik</span>
            </div>
            <div className="flex items-center justify-center space-x-2">
              <div className="w-4 h-4 bg-green-500 rounded"></div>
              <span><strong>B (Buang):</strong> Hembuskan napas perlahan selama 4 detik</span>
            </div>
          </div>
        </div>

        <Button
          onClick={() => setCurrentPhase('breathing')}
          className="bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600"
        >
          Mulai Latihan Pernapasan
        </Button>
      </div>
    </div>
  )

  // Post-test component
  const PostTestComponent = () => (
    <div className="text-center space-y-6">
      <div className="w-32 h-32 mx-auto rounded-full bg-gradient-to-br from-purple-400 to-purple-600 flex items-center justify-center">
        <div className="text-white text-4xl">✅</div>
      </div>
      <div className="space-y-4">
        <h3 className="text-2xl font-semibold text-gray-800">Setelah Latihan Pernapasan</h3>
        <p className="text-gray-600">Bagaimana perasaan Anda sekarang?</p>

        <div className="bg-gray-50 p-6 rounded-lg space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Bagaimana perasaan Anda sekarang?
            </label>
            <Textarea
              value={postTestData.feeling}
              onChange={(e) => setPostTestData(prev => ({ ...prev, feeling: e.target.value }))}
              placeholder="Contoh: Saya merasa lebih tenang dan rileks..."
              className="resize-none h-20"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Seberapa tenang perasaan Anda sekarang? (1 = Sangat Cemas, 10 = Sangat Tenang)
            </label>
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-500">1</span>
              <input
                type="range"
                min="1"
                max="10"
                value={postTestData.score}
                onChange={(e) => setPostTestData(prev => ({ ...prev, score: parseInt(e.target.value) }))}
                className="flex-1"
              />
              <span className="text-sm text-gray-500">10</span>
            </div>
            <div className="text-center mt-2">
              <span className="text-2xl font-bold text-purple-600">{postTestData.score}</span>
            </div>
          </div>

          {/* Comparison with pre-test */}
          <div className="bg-blue-50 p-4 rounded-lg">
            <h4 className="font-medium text-blue-800 mb-2">Perbandingan:</h4>
            <div className="text-sm space-y-1">
              <div>Sebelum: <span className="font-bold">{preTestData.score}/10</span></div>
              <div>Sekarang: <span className="font-bold">{postTestData.score}/10</span></div>
              <div className={`font-bold ${postTestData.score > preTestData.score ? 'text-green-600' : postTestData.score < preTestData.score ? 'text-red-600' : 'text-gray-600'}`}>
                Perubahan: {postTestData.score > preTestData.score ? '+' : ''}{postTestData.score - preTestData.score}
              </div>
            </div>
          </div>
        </div>

        <Button
          onClick={() => setCurrentPhase('completed')}
          disabled={!postTestData.feeling.trim()}
          className="bg-gradient-to-r from-purple-500 to-indigo-500 hover:from-purple-600 hover:to-indigo-600"
        >
          Selesai
        </Button>
      </div>
    </div>
  )

  // Completed component
  const CompletedComponent = () => (
    <div className="text-center space-y-6">
      <div className="w-32 h-32 mx-auto rounded-full bg-gradient-to-br from-green-400 to-emerald-600 flex items-center justify-center">
        <div className="text-white text-4xl">🎉</div>
      </div>
      <div className="space-y-4">
        <h3 className="text-2xl font-semibold text-gray-800">Latihan Pernapasan Selesai!</h3>
        <p className="text-gray-600">Terima kasih telah mengikuti latihan pernapasan</p>

        <div className="bg-gradient-to-r from-green-50 to-emerald-50 p-6 rounded-lg space-y-4">
          <h4 className="font-semibold text-green-800">Ringkasan Hasil:</h4>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div className="bg-white p-3 rounded">
              <div className="text-gray-600">Sebelum Latihan</div>
              <div className="text-2xl font-bold text-blue-600">{preTestData.score}/10</div>
            </div>
            <div className="bg-white p-3 rounded">
              <div className="text-gray-600">Setelah Latihan</div>
              <div className="text-2xl font-bold text-purple-600">{postTestData.score}/10</div>
            </div>
          </div>
          <div className={`text-center p-3 rounded font-bold ${postTestData.score > preTestData.score ? 'bg-green-100 text-green-800' : postTestData.score < preTestData.score ? 'bg-red-100 text-red-800' : 'bg-gray-100 text-gray-800'}`}>
            {postTestData.score > preTestData.score ? '✅ Perasaan Anda membaik!' : postTestData.score < preTestData.score ? '⚠️ Mungkin perlu latihan lebih lanjut' : '➡️ Perasaan Anda stabil'}
          </div>
        </div>

        <Button
          onClick={onBreakComplete}
          className="bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600 text-lg px-8 py-3"
        >
          Mulai Interview
        </Button>
      </div>
    </div>
  )

  // Breathing exercise for high stress
  const BreathingBreak = () => {
    const [count, setCount] = useState(0)
    const [cycleCount, setCycleCount] = useState(0)
    const [isDone, setIsDone] = useState(false)
    const [isStarted, setIsStarted] = useState(false)

    useEffect(() => {
      if (!isBreakActive || !isStarted) return

      const interval = setInterval(() => {
        setCount(prev => {
          const newCount = prev + 1
          setBreakTimer(newCount)

          if (newCount <= 4) {
            setBreathingPhase('inhale')
          } else if (newCount <= 6) {
            setBreathingPhase('hold')
          } else if (newCount <= 10) {
            setBreathingPhase('exhale')
          } else {
            setCount(0)
            setCycleCount(prevCycle => prevCycle + 1)
            return 0
          }

          if (newCount >= 60) { // 1 minute break
            setIsBreakActive(false)
            setIsDone(true)
            setIsBreakCompleted(true)
          }

          return newCount
        })
      }, 1000)

      return () => clearInterval(interval)
    }, [isBreakActive, isStarted])

    const getBreathingInstruction = () => {
      switch (breathingPhase) {
        case 'inhale': return 'Tarik napas dalam-dalam...'
        case 'hold': return 'Tahan napas...'
        case 'exhale': return 'Hembuskan perlahan...'
      }
    }

    const getBreathingColor = () => {
      switch (breathingPhase) {
        case 'inhale': return 'from-blue-400 to-blue-600'
        case 'hold': return 'from-yellow-400 to-yellow-600'
        case 'exhale': return 'from-green-400 to-green-600'
      }
    }

    const getPhaseSeconds = () => {
      switch (breathingPhase) {
        case 'inhale': return 4
        case 'hold': return 2
        case 'exhale': return 4
      }
    }

    const getCurrentPhaseProgress = () => {
      const phaseStart = breathingPhase === 'inhale' ? 0 : breathingPhase === 'hold' ? 4 : 6
      const currentInPhase = count - phaseStart
      return Math.min(currentInPhase, getPhaseSeconds())
    }

    const getPhaseIcon = () => {
      switch (breathingPhase) {
        case 'inhale': return '↑'
        case 'hold': return '⏸'
        case 'exhale': return '↓'
        default: return '○'
      }
    }

    // Modern breathing pattern cards
    const BreathingCards = () => {
      const phases = [
        { name: 'Tarik', duration: 4, color: 'bg-blue-500', textColor: 'text-blue-600', bgColor: 'bg-blue-50', icon: '↑' },
        { name: 'Tahan', duration: 2, color: 'bg-yellow-500', textColor: 'text-yellow-600', bgColor: 'bg-yellow-50', icon: '⏸' },
        { name: 'Hembus', duration: 4, color: 'bg-green-500', textColor: 'text-green-600', bgColor: 'bg-green-50', icon: '↓' }
      ]

      const getCurrentPhaseIndex = () => {
        if (breathingPhase === 'inhale') return 0
        if (breathingPhase === 'hold') return 1
        if (breathingPhase === 'exhale') return 2
        return -1
      }

      return (
        <div className="grid grid-cols-2 gap-4 max-w-md mx-auto mb-6">
          {phases.map((phase, index) => {
            const isActive = getCurrentPhaseIndex() === index
            const isCompleted = getCurrentPhaseIndex() > index

            return (
              <div
                key={phase.name}
                className={`relative p-4 rounded-xl border-2 transition-all duration-500 ${
                  isActive
                    ? `${phase.bgColor} border-${phase.color.replace('bg-', '')} shadow-lg scale-105`
                    : isCompleted
                    ? 'bg-gray-100 border-gray-300'
                    : 'bg-white border-gray-200'
                }`}
              >
                <div className="text-center">
                  <div className={`text-2xl mb-1 ${isActive ? phase.textColor : 'text-gray-400'}`}>
                    {phase.icon}
                  </div>
                  <div className={`font-semibold text-lg ${isActive ? phase.textColor : 'text-gray-600'}`}>
                    {phase.name}
                  </div>
                  <div className={`text-sm ${isActive ? phase.textColor : 'text-gray-500'}`}>
                    {phase.duration} detik
                  </div>
                  {isActive && (
                    <div className={`mt-2 w-8 h-8 mx-auto rounded-full ${phase.color} flex items-center justify-center text-white font-bold animate-pulse`}>
                      {getCurrentPhaseProgress()}
                    </div>
                  )}
                </div>
                {isCompleted && (
                  <div className="absolute top-2 right-2 w-5 h-5 bg-green-500 rounded-full flex items-center justify-center">
                    <span className="text-white text-xs">✓</span>
                  </div>
                )}
              </div>
            )
          })}
        </div>
      )
    }

    // Show instructions first, then breathing exercise
    if (!isStarted) {
      return (
        <div className="text-center space-y-8">
          {/* Heart icon */}
          <div className="w-20 h-20 mx-auto bg-gradient-to-br from-pink-400 to-red-500 rounded-full flex items-center justify-center">
            <Heart className="h-10 w-10 text-white" />
          </div>

          {/* Title */}
          <div className="space-y-2">
            <h2 className="text-2xl font-bold text-gray-800">Latihan Pernapasan</h2>
            <p className="text-gray-600 max-w-md mx-auto">
              Mari kita mulai dengan latihan pernapasan untuk membantu Anda merasa lebih tenang dan fokus.
            </p>
          </div>

          {/* Breathing pattern cards */}
          <BreathingCards />

          {/* Cycle info */}
          <div className="bg-blue-50 p-4 rounded-xl border border-blue-200">
            <p className="text-blue-800 font-medium">Siklus 1 dari 4</p>
            <p className="text-blue-600 text-sm mt-1">Total durasi: 1 menit</p>
          </div>

          {/* Start button */}
          <Button
            onClick={() => setIsStarted(true)}
            className="bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 text-lg px-8 py-3 rounded-xl"
          >
            Mulai Latihan
          </Button>

          {/* Skip option */}
          <Button
            variant="outline"
            onClick={() => setCurrentPhase('post-test')}
            className="text-gray-500 hover:text-gray-700"
          >
            Lanjut ke wawancara
          </Button>
        </div>
      )
    }

    return (
      <div className="text-center space-y-6">
        {/* Main breathing circle */}
        <div className={`w-40 h-40 mx-auto rounded-full bg-gradient-to-br ${getBreathingColor()} flex items-center justify-center transition-all duration-1000 shadow-2xl ${breathingPhase === 'inhale' ? 'scale-110' : breathingPhase === 'exhale' ? 'scale-90' : 'scale-100'}`}>
          <div className="text-white text-center">
            <div className="text-4xl font-bold">{getCurrentPhaseProgress()}</div>
            <div className="text-sm opacity-80">{getPhaseSeconds()}s</div>
            <div className="text-2xl mt-1">{getPhaseIcon()}</div>
          </div>
        </div>

        {/* Current instruction */}
        <div className="space-y-2">
          <p className="text-2xl font-medium text-gray-800">{getBreathingInstruction()}</p>
          <p className="text-gray-500">Ikuti ritme pernapasan dengan tenang</p>
        </div>

        {/* Breathing pattern cards */}
        <BreathingCards />

        {/* Progress info */}
        <div className="bg-gray-50 p-4 rounded-xl">
          <div className="text-sm text-gray-600 mb-2">
            Siklus ke-{cycleCount + 1} dari 6 | {getCurrentPhaseProgress()}/{getPhaseSeconds()} detik
          </div>
          <Progress value={(breakTimer / 60) * 100} className="w-full" />
          <p className="text-xs text-gray-500 mt-2">
            {isDone ? 'Latihan pernapasan selesai!' : `Sisa waktu: ${60 - breakTimer} detik`}
          </p>
        </div>

        {isDone && (
          <div className="space-y-4">
            <div className="p-4 bg-green-50 border border-green-200 rounded-xl">
              <p className="text-green-800 font-medium">✓ Latihan pernapasan selesai!</p>
              <p className="text-green-600 text-sm">Anda telah menyelesaikan 6 siklus pernapasan</p>
            </div>
            <Button
              onClick={() => setCurrentPhase('post-test')}
              className="bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600 text-lg px-8 py-3 rounded-xl"
            >
              Lanjutkan ke Evaluasi
            </Button>
          </div>
        )}
      </div>
    )
  }

  // Affirmation break for encouragement
  const AffirmationBreak = () => {
    const affirmations = [
      "Anda sedang melakukan dengan sangat baik",
      "Setiap jawaban Anda menunjukkan kemampuan yang luar biasa",
      "Pengalaman Anda sangat berharga dan unik",
      "Anda memiliki potensi yang besar",
      "Kepercayaan diri Anda terus berkembang",
      "Anda adalah kandidat yang berkualitas"
    ]

    const [currentAffirmation, setCurrentAffirmation] = useState(0)
    const [isDone, setIsDone] = useState(false)

    useEffect(() => {
      if (!isBreakActive) return

      const interval = setInterval(() => {
        setBreakTimer(prev => {
          const newTimer = prev + 1
          if (newTimer % 5 === 0 && newTimer < 30) {
            setCurrentAffirmation(Math.floor(newTimer / 5) % affirmations.length)
          }
          if (newTimer >= 30) {
            setIsBreakActive(false)
            setIsDone(true)
            setIsBreakCompleted(true)
          }
          return newTimer
        })
      }, 1000)

      return () => clearInterval(interval)
    }, [isBreakActive])

    return (
      <div className="text-center space-y-6">
        <div className="w-32 h-32 mx-auto rounded-full bg-gradient-to-br from-green-400 to-emerald-600 flex items-center justify-center">
          <Shield className="h-12 w-12 text-white" />
        </div>
        <div className="space-y-4">
          <h3 className="text-xl font-semibold text-gray-800">Pesan Positif untuk Anda</h3>
          <p className="text-lg text-gray-700 font-medium bg-green-50 p-4 rounded-lg">
            {affirmations[currentAffirmation]}
          </p>
          <p className="text-sm text-gray-500">
            {isDone ? 'Sesi afirmasi selesai!' : `Sisa waktu: ${30 - breakTimer} detik`}
          </p>
        </div>
        <Progress value={(breakTimer / 30) * 100} className="w-64 mx-auto" />

        {isDone && (
          <Button
            onClick={onBreakComplete}
            className="bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600"
          >
            Selesai - Lanjutkan Interview
          </Button>
        )}
      </div>
    )
  }

  // Rest break for general relaxation
  const RestBreak = () => {
    const [isDone, setIsDone] = useState(false)

    useEffect(() => {
      if (!isBreakActive) return

      const interval = setInterval(() => {
        setBreakTimer(prev => {
          const newTimer = prev + 1
          if (newTimer >= 20) {
            setIsBreakActive(false)
            setIsDone(true)
            setIsBreakCompleted(true)
          }
          return newTimer
        })
      }, 1000)

      return () => clearInterval(interval)
    }, [isBreakActive])

    return (
      <div className="text-center space-y-6">
        <div className="w-32 h-32 mx-auto rounded-full bg-gradient-to-br from-purple-400 to-indigo-600 flex items-center justify-center">
          <Coffee className="h-12 w-12 text-white" />
        </div>
        <div className="space-y-4">
          <h3 className="text-xl font-semibold text-gray-800">Waktu Istirahat Sejenak</h3>
          <p className="text-gray-600 max-w-md mx-auto">
            Ambil waktu sejenak untuk rileks. Anda sudah melakukan dengan baik sejauh ini.
          </p>
          <p className="text-sm text-gray-500">
            {isDone ? 'Waktu istirahat selesai!' : `Sisa waktu: ${20 - breakTimer} detik`}
          </p>
        </div>
        <Progress value={(breakTimer / 20) * 100} className="w-64 mx-auto" />

        {isDone && (
          <Button
            onClick={onBreakComplete}
            className="bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600"
          >
            Selesai - Lanjutkan Interview
          </Button>
        )}
      </div>
    )
  }

  if (!isVisible) return null

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
      <Card className="w-full max-w-2xl">
        <CardHeader className="bg-gradient-to-r from-blue-500 to-purple-500 text-white text-center">
          <CardTitle className="text-2xl font-bold">Psychological Safety Break</CardTitle>
          <p className="text-blue-100">Mari ambil waktu sejenak untuk diri Anda</p>
        </CardHeader>
        <CardContent className="p-8">
          {currentPhase === 'pre-test' && <PreTestComponent />}
          {currentPhase === 'instructions' && <InstructionsComponent />}
          {currentPhase === 'breathing' && (
            <>
              {breakType === 'breathing' && <BreathingBreak />}
              {breakType === 'affirmation' && <AffirmationBreak />}
              {breakType === 'rest' && <RestBreak />}
            </>
          )}
          {currentPhase === 'post-test' && <PostTestComponent />}
          {currentPhase === 'completed' && <CompletedComponent />}

          {currentPhase === 'breathing' && !isBreakCompleted && (
            <div className="mt-8 text-center">
              <Button
                variant="outline"
                onClick={onClose}
                className="mr-4"
              >
                Lewati Break
              </Button>
              <Button
                onClick={() => {
                  setIsBreakActive(false)
                  setIsBreakCompleted(true)
                }}
                disabled={isBreakActive}
                className="bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600"
              >
                {isBreakActive ? 'Sedang Break...' : 'Selesaikan Break'}
              </Button>
            </div>
          )}

          {currentPhase === 'breathing' && isBreakCompleted && (
            <div className="mt-8 text-center">
              <div className="mb-4 p-3 bg-green-50 border border-green-200 rounded-lg">
                <p className="text-green-800 font-medium">✓ Break selesai! Lanjutkan ke evaluasi.</p>
              </div>
              <Button
                onClick={() => setCurrentPhase('post-test')}
                className="bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600 text-lg px-8 py-3"
              >
                Lanjutkan ke Evaluasi
              </Button>
            </div>
          )}

          {(currentPhase === 'pre-test' || currentPhase === 'instructions') && (
            <div className="mt-8 text-center">
              <Button
                variant="outline"
                onClick={onClose}
              >
                Lewati Semua
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
