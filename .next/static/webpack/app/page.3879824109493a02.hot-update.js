"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./components/psychological-safety.tsx":
/*!*********************************************!*\
  !*** ./components/psychological-safety.tsx ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PsychologicalSafety)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_progress__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/progress */ \"(app-pages-browser)/./components/ui/progress.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./components/ui/textarea.tsx\");\n/* harmony import */ var _barrel_optimize_names_Coffee_Heart_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Coffee,Heart,Shield!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_Coffee_Heart_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Coffee,Heart,Shield!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Coffee_Heart_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Coffee,Heart,Shield!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/coffee.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction PsychologicalSafety(param) {\n    let { isVisible, onClose, onBreakComplete, profile, stressLevel = 'medium' } = param;\n    _s();\n    var _s1 = $RefreshSig$(), _s2 = $RefreshSig$(), _s3 = $RefreshSig$();\n    const [breakType, setBreakType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('breathing');\n    const [breakTimer, setBreakTimer] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [isBreakActive, setIsBreakActive] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isBreakCompleted, setIsBreakCompleted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [breathingPhase, setBreathingPhase] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('inhale');\n    // Pre-post test states\n    const [currentPhase, setCurrentPhase] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('pre-test');\n    const [preTestData, setPreTestData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        feeling: '',\n        score: 5\n    });\n    const [postTestData, setPostTestData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        feeling: '',\n        score: 5\n    });\n    // Auto-start break when component becomes visible\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PsychologicalSafety.useEffect\": ()=>{\n            if (isVisible && currentPhase === 'breathing' && !isBreakActive) {\n                startBreak();\n            }\n        }\n    }[\"PsychologicalSafety.useEffect\"], [\n        isVisible,\n        currentPhase\n    ]);\n    const startBreak = ()=>{\n        setIsBreakActive(true);\n        setBreakTimer(0);\n        // Choose break type based on stress level and profile\n        if (stressLevel === 'high' || profile && profile.anxietyLevel === 'high') {\n            setBreakType('breathing');\n        } else if (profile && profile.needsEncouragement) {\n            setBreakType('affirmation');\n        } else {\n            setBreakType('rest');\n        }\n    };\n    // Pre-test component\n    const PreTestComponent = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-32 h-32 mx-auto rounded-full bg-gradient-to-br from-blue-400 to-blue-600 flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-white text-4xl\",\n                        children: \"\\uD83D\\uDCDD\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                    lineNumber: 68,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-2xl font-semibold text-gray-800\",\n                            children: \"Sebelum Latihan Pernapasan\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                            lineNumber: 72,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: \"Mari kita ukur perasaan Anda saat ini\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                            lineNumber: 73,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-50 p-6 rounded-lg space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Bagaimana perasaan Anda saat ini?\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                            lineNumber: 77,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_5__.Textarea, {\n                                            value: preTestData.feeling,\n                                            onChange: (e)=>setPreTestData((prev)=>({\n                                                        ...prev,\n                                                        feeling: e.target.value\n                                                    })),\n                                            placeholder: \"Contoh: Saya merasa cemas dan gugup...\",\n                                            className: \"resize-none h-20\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                            lineNumber: 80,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                    lineNumber: 76,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Seberapa tenang perasaan Anda? (1 = Sangat Cemas, 10 = Sangat Tenang)\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                            lineNumber: 89,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-gray-500\",\n                                                    children: \"1\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                                    lineNumber: 93,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"range\",\n                                                    min: \"1\",\n                                                    max: \"10\",\n                                                    value: preTestData.score,\n                                                    onChange: (e)=>setPreTestData((prev)=>({\n                                                                ...prev,\n                                                                score: parseInt(e.target.value)\n                                                            })),\n                                                    className: \"flex-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                                    lineNumber: 94,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-gray-500\",\n                                                    children: \"10\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                                    lineNumber: 102,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                            lineNumber: 92,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center mt-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-2xl font-bold text-blue-600\",\n                                                children: preTestData.score\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                                lineNumber: 105,\n                                                columnNumber: 15\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                            lineNumber: 104,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                    lineNumber: 88,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                            lineNumber: 75,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            onClick: ()=>setCurrentPhase('instructions'),\n                            disabled: !preTestData.feeling.trim(),\n                            className: \"bg-gradient-to-r from-blue-500 to-indigo-500 hover:from-blue-600 hover:to-indigo-600\",\n                            children: \"Lanjutkan ke Instruksi\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                            lineNumber: 110,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                    lineNumber: 71,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n            lineNumber: 67,\n            columnNumber: 5\n        }, this);\n    // Instructions component with visual grid\n    const InstructionsComponent = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-32 h-32 mx-auto rounded-full bg-gradient-to-br from-green-400 to-emerald-600 flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-white text-4xl\",\n                        children: \"\\uD83D\\uDCCB\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                        lineNumber: 125,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                    lineNumber: 124,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-2xl font-semibold text-gray-800\",\n                            children: \"Instruksi Latihan Pernapasan\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                            lineNumber: 128,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: \"Ikuti pola pernapasan berikut untuk merasa lebih tenang\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                            lineNumber: 129,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-50 p-6 rounded-lg space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-600 mb-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Pola Pernapasan 4-3-4:\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                        lineNumber: 133,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                    lineNumber: 132,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-4 gap-2 w-48 mx-auto mb-4\",\n                                    children: Array.from({\n                                        length: 12\n                                    }, (_, i)=>{\n                                        let squareColor = 'bg-gray-200';\n                                        let label = '';\n                                        if (i < 4) {\n                                            squareColor = 'bg-blue-500';\n                                            label = 'T';\n                                        } else if (i < 7) {\n                                            squareColor = 'bg-yellow-500';\n                                            label = 'H';\n                                        } else if (i < 11) {\n                                            squareColor = 'bg-green-500';\n                                            label = 'B';\n                                        } else {\n                                            squareColor = 'bg-gray-300';\n                                            label = '↻';\n                                        }\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-10 h-10 \".concat(squareColor, \" rounded flex items-center justify-center text-white font-bold text-sm\"),\n                                            children: label\n                                        }, i, false, {\n                                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                            lineNumber: 157,\n                                            columnNumber: 17\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                    lineNumber: 137,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2 text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-4 h-4 bg-blue-500 rounded\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                                    lineNumber: 169,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"T (Tarik):\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                                            lineNumber: 170,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \" Tarik napas dalam-dalam selama 4 detik\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                                    lineNumber: 170,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                            lineNumber: 168,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-4 h-4 bg-yellow-500 rounded\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                                    lineNumber: 173,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"H (Tahan):\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                                            lineNumber: 174,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \" Tahan napas selama 3 detik\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                                    lineNumber: 174,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                            lineNumber: 172,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-4 h-4 bg-green-500 rounded\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                                    lineNumber: 177,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"B (Buang):\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                                            lineNumber: 178,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \" Hembuskan napas perlahan selama 4 detik\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                                    lineNumber: 178,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                            lineNumber: 176,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                    lineNumber: 167,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                            lineNumber: 131,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            onClick: ()=>setCurrentPhase('breathing'),\n                            className: \"bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600\",\n                            children: \"Mulai Latihan Pernapasan\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                            lineNumber: 183,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                    lineNumber: 127,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n            lineNumber: 123,\n            columnNumber: 5\n        }, this);\n    // Post-test component\n    const PostTestComponent = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-32 h-32 mx-auto rounded-full bg-gradient-to-br from-purple-400 to-purple-600 flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-white text-4xl\",\n                        children: \"✅\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                        lineNumber: 197,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                    lineNumber: 196,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-2xl font-semibold text-gray-800\",\n                            children: \"Setelah Latihan Pernapasan\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                            lineNumber: 200,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: \"Bagaimana perasaan Anda sekarang?\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                            lineNumber: 201,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-50 p-6 rounded-lg space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Bagaimana perasaan Anda sekarang?\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                            lineNumber: 205,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_5__.Textarea, {\n                                            value: postTestData.feeling,\n                                            onChange: (e)=>setPostTestData((prev)=>({\n                                                        ...prev,\n                                                        feeling: e.target.value\n                                                    })),\n                                            placeholder: \"Contoh: Saya merasa lebih tenang dan rileks...\",\n                                            className: \"resize-none h-20\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                            lineNumber: 208,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                    lineNumber: 204,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Seberapa tenang perasaan Anda sekarang? (1 = Sangat Cemas, 10 = Sangat Tenang)\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                            lineNumber: 217,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-gray-500\",\n                                                    children: \"1\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                                    lineNumber: 221,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"range\",\n                                                    min: \"1\",\n                                                    max: \"10\",\n                                                    value: postTestData.score,\n                                                    onChange: (e)=>setPostTestData((prev)=>({\n                                                                ...prev,\n                                                                score: parseInt(e.target.value)\n                                                            })),\n                                                    className: \"flex-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                                    lineNumber: 222,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-gray-500\",\n                                                    children: \"10\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                                    lineNumber: 230,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                            lineNumber: 220,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center mt-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-2xl font-bold text-purple-600\",\n                                                children: postTestData.score\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                                lineNumber: 233,\n                                                columnNumber: 15\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                            lineNumber: 232,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                    lineNumber: 216,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-blue-50 p-4 rounded-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-medium text-blue-800 mb-2\",\n                                            children: \"Perbandingan:\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                            lineNumber: 239,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm space-y-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        \"Sebelum: \",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-bold\",\n                                                            children: [\n                                                                preTestData.score,\n                                                                \"/10\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                                            lineNumber: 241,\n                                                            columnNumber: 29\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                                    lineNumber: 241,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        \"Sekarang: \",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-bold\",\n                                                            children: [\n                                                                postTestData.score,\n                                                                \"/10\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                                            lineNumber: 242,\n                                                            columnNumber: 30\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                                    lineNumber: 242,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"font-bold \".concat(postTestData.score > preTestData.score ? 'text-green-600' : postTestData.score < preTestData.score ? 'text-red-600' : 'text-gray-600'),\n                                                    children: [\n                                                        \"Perubahan: \",\n                                                        postTestData.score > preTestData.score ? '+' : '',\n                                                        postTestData.score - preTestData.score\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                                    lineNumber: 243,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                            lineNumber: 240,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                    lineNumber: 238,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                            lineNumber: 203,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            onClick: ()=>setCurrentPhase('completed'),\n                            disabled: !postTestData.feeling.trim(),\n                            className: \"bg-gradient-to-r from-purple-500 to-indigo-500 hover:from-purple-600 hover:to-indigo-600\",\n                            children: \"Selesai\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                            lineNumber: 250,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                    lineNumber: 199,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n            lineNumber: 195,\n            columnNumber: 5\n        }, this);\n    // Completed component\n    const CompletedComponent = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-32 h-32 mx-auto rounded-full bg-gradient-to-br from-green-400 to-emerald-600 flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-white text-4xl\",\n                        children: \"\\uD83C\\uDF89\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                        lineNumber: 265,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                    lineNumber: 264,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-2xl font-semibold text-gray-800\",\n                            children: \"Latihan Pernapasan Selesai!\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                            lineNumber: 268,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: \"Terima kasih telah mengikuti latihan pernapasan\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                            lineNumber: 269,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gradient-to-r from-green-50 to-emerald-50 p-6 rounded-lg space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"font-semibold text-green-800\",\n                                    children: \"Ringkasan Hasil:\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                    lineNumber: 272,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-2 gap-4 text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white p-3 rounded\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-gray-600\",\n                                                    children: \"Sebelum Latihan\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                                    lineNumber: 275,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl font-bold text-blue-600\",\n                                                    children: [\n                                                        preTestData.score,\n                                                        \"/10\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                                    lineNumber: 276,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                            lineNumber: 274,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white p-3 rounded\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-gray-600\",\n                                                    children: \"Setelah Latihan\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                                    lineNumber: 279,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl font-bold text-purple-600\",\n                                                    children: [\n                                                        postTestData.score,\n                                                        \"/10\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                                    lineNumber: 280,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                            lineNumber: 278,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                    lineNumber: 273,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center p-3 rounded font-bold \".concat(postTestData.score > preTestData.score ? 'bg-green-100 text-green-800' : postTestData.score < preTestData.score ? 'bg-red-100 text-red-800' : 'bg-gray-100 text-gray-800'),\n                                    children: postTestData.score > preTestData.score ? '✅ Perasaan Anda membaik!' : postTestData.score < preTestData.score ? '⚠️ Mungkin perlu latihan lebih lanjut' : '➡️ Perasaan Anda stabil'\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                    lineNumber: 283,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                            lineNumber: 271,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            onClick: onBreakComplete,\n                            className: \"bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600 text-lg px-8 py-3\",\n                            children: \"Mulai Interview\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                            lineNumber: 288,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                    lineNumber: 267,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n            lineNumber: 263,\n            columnNumber: 5\n        }, this);\n    // Breathing exercise for high stress\n    const BreathingBreak = ()=>{\n        _s1();\n        const [count, setCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n        const [cycleCount, setCycleCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n        const [isDone, setIsDone] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n        const [isStarted, setIsStarted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n        const [totalTimer, setTotalTimer] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n        const TOTAL_CYCLES = 3;\n        const CYCLE_DURATION = 10 // 4 + 2 + 4 seconds\n        ;\n        (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n            \"PsychologicalSafety.BreathingBreak.useEffect\": ()=>{\n                if (!isBreakActive || !isStarted) return;\n                const interval = setInterval({\n                    \"PsychologicalSafety.BreathingBreak.useEffect.interval\": ()=>{\n                        setCount({\n                            \"PsychologicalSafety.BreathingBreak.useEffect.interval\": (prev)=>{\n                                const newCount = prev + 1;\n                                setTotalTimer({\n                                    \"PsychologicalSafety.BreathingBreak.useEffect.interval\": (prevTotal)=>prevTotal + 1\n                                }[\"PsychologicalSafety.BreathingBreak.useEffect.interval\"]);\n                                if (newCount <= 4) {\n                                    setBreathingPhase('inhale');\n                                } else if (newCount <= 6) {\n                                    setBreathingPhase('hold');\n                                } else if (newCount <= 10) {\n                                    setBreathingPhase('exhale');\n                                } else {\n                                    // Complete one cycle\n                                    setCycleCount({\n                                        \"PsychologicalSafety.BreathingBreak.useEffect.interval\": (prevCycle)=>{\n                                            const newCycleCount = prevCycle + 1;\n                                            // Check if we've completed all cycles\n                                            if (newCycleCount >= TOTAL_CYCLES) {\n                                                setIsBreakActive(false);\n                                                setIsDone(true);\n                                                setIsBreakCompleted(true);\n                                            }\n                                            return newCycleCount;\n                                        }\n                                    }[\"PsychologicalSafety.BreathingBreak.useEffect.interval\"]);\n                                    setCount(0);\n                                    return 0;\n                                }\n                                return newCount;\n                            }\n                        }[\"PsychologicalSafety.BreathingBreak.useEffect.interval\"]);\n                    }\n                }[\"PsychologicalSafety.BreathingBreak.useEffect.interval\"], 1000);\n                return ({\n                    \"PsychologicalSafety.BreathingBreak.useEffect\": ()=>clearInterval(interval)\n                })[\"PsychologicalSafety.BreathingBreak.useEffect\"];\n            }\n        }[\"PsychologicalSafety.BreathingBreak.useEffect\"], [\n            isBreakActive,\n            isStarted\n        ]);\n        const getBreathingInstruction = ()=>{\n            switch(breathingPhase){\n                case 'inhale':\n                    return 'Tarik napas dalam-dalam...';\n                case 'hold':\n                    return 'Tahan napas...';\n                case 'exhale':\n                    return 'Hembuskan perlahan...';\n            }\n        };\n        const getBreathingColor = ()=>{\n            switch(breathingPhase){\n                case 'inhale':\n                    return 'from-blue-400 to-blue-600';\n                case 'hold':\n                    return 'from-yellow-400 to-yellow-600';\n                case 'exhale':\n                    return 'from-green-400 to-green-600';\n            }\n        };\n        const getPhaseSeconds = ()=>{\n            switch(breathingPhase){\n                case 'inhale':\n                    return 4;\n                case 'hold':\n                    return 2;\n                case 'exhale':\n                    return 4;\n            }\n        };\n        const getCurrentPhaseProgress = ()=>{\n            const phaseStart = breathingPhase === 'inhale' ? 0 : breathingPhase === 'hold' ? 4 : 6;\n            const currentInPhase = count - phaseStart;\n            return Math.min(currentInPhase, getPhaseSeconds());\n        };\n        const getPhaseIcon = ()=>{\n            switch(breathingPhase){\n                case 'inhale':\n                    return '↑';\n                case 'hold':\n                    return '⏸';\n                case 'exhale':\n                    return '↓';\n                default:\n                    return '○';\n            }\n        };\n        // Modern breathing pattern cards\n        const BreathingCards = ()=>{\n            const phases = [\n                {\n                    name: 'Tarik',\n                    duration: 4,\n                    color: 'bg-blue-500',\n                    textColor: 'text-blue-600',\n                    bgColor: 'bg-blue-50',\n                    icon: '↑'\n                },\n                {\n                    name: 'Tahan',\n                    duration: 2,\n                    color: 'bg-yellow-500',\n                    textColor: 'text-yellow-600',\n                    bgColor: 'bg-yellow-50',\n                    icon: '⏸'\n                },\n                {\n                    name: 'Hembus',\n                    duration: 4,\n                    color: 'bg-green-500',\n                    textColor: 'text-green-600',\n                    bgColor: 'bg-green-50',\n                    icon: '↓'\n                }\n            ];\n            const getCurrentPhaseIndex = ()=>{\n                if (breathingPhase === 'inhale') return 0;\n                if (breathingPhase === 'hold') return 1;\n                if (breathingPhase === 'exhale') return 2;\n                return -1;\n            };\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-2 gap-4 max-w-md mx-auto mb-6\",\n                children: phases.map((phase, index)=>{\n                    const isActive = getCurrentPhaseIndex() === index;\n                    const isCompleted = getCurrentPhaseIndex() > index;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative p-4 rounded-xl border-2 transition-all duration-500 \".concat(isActive ? \"\".concat(phase.bgColor, \" border-\").concat(phase.color.replace('bg-', ''), \" shadow-lg scale-105\") : isCompleted ? 'bg-gray-100 border-gray-300' : 'bg-white border-gray-200'),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl mb-1 \".concat(isActive ? phase.textColor : 'text-gray-400'),\n                                        children: phase.icon\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                        lineNumber: 420,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"font-semibold text-lg \".concat(isActive ? phase.textColor : 'text-gray-600'),\n                                        children: phase.name\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                        lineNumber: 423,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm \".concat(isActive ? phase.textColor : 'text-gray-500'),\n                                        children: [\n                                            phase.duration,\n                                            \" detik\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                        lineNumber: 426,\n                                        columnNumber: 19\n                                    }, this),\n                                    isActive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-2 w-8 h-8 mx-auto rounded-full \".concat(phase.color, \" flex items-center justify-center text-white font-bold animate-pulse\"),\n                                        children: getCurrentPhaseProgress()\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                        lineNumber: 430,\n                                        columnNumber: 21\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                lineNumber: 419,\n                                columnNumber: 17\n                            }, this),\n                            isCompleted && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-2 right-2 w-5 h-5 bg-green-500 rounded-full flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-white text-xs\",\n                                    children: \"✓\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                    lineNumber: 437,\n                                    columnNumber: 21\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                lineNumber: 436,\n                                columnNumber: 19\n                            }, this)\n                        ]\n                    }, phase.name, true, {\n                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                        lineNumber: 409,\n                        columnNumber: 15\n                    }, this);\n                })\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                lineNumber: 403,\n                columnNumber: 9\n            }, this);\n        };\n        // Show instructions first, then breathing exercise\n        if (!isStarted) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center space-y-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-20 h-20 mx-auto bg-gradient-to-br from-pink-400 to-red-500 rounded-full flex items-center justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Coffee_Heart_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            className: \"h-10 w-10 text-white\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                            lineNumber: 453,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                        lineNumber: 452,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold text-gray-800\",\n                                children: \"Latihan Pernapasan\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                lineNumber: 458,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 max-w-md mx-auto\",\n                                children: \"Mari kita mulai dengan latihan pernapasan untuk membantu Anda merasa lebih tenang dan fokus.\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                lineNumber: 459,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                        lineNumber: 457,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BreathingCards, {}, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                        lineNumber: 465,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-blue-50 p-4 rounded-xl border border-blue-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-blue-800 font-medium\",\n                                children: [\n                                    \"Siklus 1 dari \",\n                                    TOTAL_CYCLES\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                lineNumber: 469,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-blue-600 text-sm mt-1\",\n                                children: [\n                                    \"Total durasi: \",\n                                    TOTAL_CYCLES * CYCLE_DURATION,\n                                    \" detik\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                lineNumber: 470,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                        lineNumber: 468,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                        onClick: ()=>setIsStarted(true),\n                        className: \"bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 text-lg px-8 py-3 rounded-xl\",\n                        children: \"Mulai Latihan\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                        lineNumber: 474,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                        variant: \"outline\",\n                        onClick: ()=>setCurrentPhase('post-test'),\n                        className: \"text-gray-500 hover:text-gray-700\",\n                        children: \"Lanjut ke wawancara\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                        lineNumber: 482,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                lineNumber: 450,\n                columnNumber: 9\n            }, this);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-40 h-40 mx-auto rounded-full bg-gradient-to-br \".concat(getBreathingColor(), \" flex items-center justify-center transition-all duration-1000 shadow-2xl \").concat(breathingPhase === 'inhale' ? 'scale-110' : breathingPhase === 'exhale' ? 'scale-90' : 'scale-100'),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-white text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-4xl font-bold\",\n                                children: getCurrentPhaseProgress()\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                lineNumber: 498,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm opacity-80\",\n                                children: [\n                                    getPhaseSeconds(),\n                                    \"s\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                lineNumber: 499,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-2xl mt-1\",\n                                children: getPhaseIcon()\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                lineNumber: 500,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                        lineNumber: 497,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                    lineNumber: 496,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-2xl font-medium text-gray-800\",\n                            children: getBreathingInstruction()\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                            lineNumber: 506,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-500\",\n                            children: \"Ikuti ritme pernapasan dengan tenang\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                            lineNumber: 507,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                    lineNumber: 505,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BreathingCards, {}, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                    lineNumber: 511,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gray-50 p-4 rounded-xl\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm text-gray-600 mb-2\",\n                            children: [\n                                \"Siklus ke-\",\n                                cycleCount + 1,\n                                \" dari \",\n                                TOTAL_CYCLES,\n                                \" | \",\n                                getCurrentPhaseProgress(),\n                                \"/\",\n                                getPhaseSeconds(),\n                                \" detik\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                            lineNumber: 515,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_progress__WEBPACK_IMPORTED_MODULE_4__.Progress, {\n                            value: (cycleCount * CYCLE_DURATION + count) / (TOTAL_CYCLES * CYCLE_DURATION) * 100,\n                            className: \"w-full\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                            lineNumber: 518,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-gray-500 mt-2\",\n                            children: isDone ? 'Latihan pernapasan selesai!' : \"Sisa waktu: \".concat(TOTAL_CYCLES * CYCLE_DURATION - (cycleCount * CYCLE_DURATION + count), \" detik\")\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                            lineNumber: 519,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                    lineNumber: 514,\n                    columnNumber: 9\n                }, this),\n                isDone && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4 bg-green-50 border border-green-200 rounded-xl\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-green-800 font-medium\",\n                                    children: \"✓ Latihan pernapasan selesai!\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                    lineNumber: 527,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-green-600 text-sm\",\n                                    children: [\n                                        \"Anda telah menyelesaikan \",\n                                        TOTAL_CYCLES,\n                                        \" siklus pernapasan\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                    lineNumber: 528,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                            lineNumber: 526,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            onClick: ()=>setCurrentPhase('post-test'),\n                            className: \"bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600 text-lg px-8 py-3 rounded-xl\",\n                            children: \"Lanjutkan ke Evaluasi\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                            lineNumber: 530,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                    lineNumber: 525,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n            lineNumber: 494,\n            columnNumber: 7\n        }, this);\n    };\n    _s1(BreathingBreak, \"T2yvTh9DD4HWYTE2zl5mQ9bRNc8=\");\n    // Affirmation break for encouragement\n    const AffirmationBreak = ()=>{\n        _s2();\n        const affirmations = [\n            \"Anda sedang melakukan dengan sangat baik\",\n            \"Setiap jawaban Anda menunjukkan kemampuan yang luar biasa\",\n            \"Pengalaman Anda sangat berharga dan unik\",\n            \"Anda memiliki potensi yang besar\",\n            \"Kepercayaan diri Anda terus berkembang\",\n            \"Anda adalah kandidat yang berkualitas\"\n        ];\n        const [currentAffirmation, setCurrentAffirmation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n        const [isDone, setIsDone] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n        (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n            \"PsychologicalSafety.AffirmationBreak.useEffect\": ()=>{\n                if (!isBreakActive) return;\n                const interval = setInterval({\n                    \"PsychologicalSafety.AffirmationBreak.useEffect.interval\": ()=>{\n                        setBreakTimer({\n                            \"PsychologicalSafety.AffirmationBreak.useEffect.interval\": (prev)=>{\n                                const newTimer = prev + 1;\n                                if (newTimer % 5 === 0 && newTimer < 30) {\n                                    setCurrentAffirmation(Math.floor(newTimer / 5) % affirmations.length);\n                                }\n                                if (newTimer >= 30) {\n                                    setIsBreakActive(false);\n                                    setIsDone(true);\n                                    setIsBreakCompleted(true);\n                                }\n                                return newTimer;\n                            }\n                        }[\"PsychologicalSafety.AffirmationBreak.useEffect.interval\"]);\n                    }\n                }[\"PsychologicalSafety.AffirmationBreak.useEffect.interval\"], 1000);\n                return ({\n                    \"PsychologicalSafety.AffirmationBreak.useEffect\": ()=>clearInterval(interval)\n                })[\"PsychologicalSafety.AffirmationBreak.useEffect\"];\n            }\n        }[\"PsychologicalSafety.AffirmationBreak.useEffect\"], [\n            isBreakActive\n        ]);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-32 h-32 mx-auto rounded-full bg-gradient-to-br from-green-400 to-emerald-600 flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Coffee_Heart_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        className: \"h-12 w-12 text-white\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                        lineNumber: 580,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                    lineNumber: 579,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-xl font-semibold text-gray-800\",\n                            children: \"Pesan Positif untuk Anda\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                            lineNumber: 583,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-lg text-gray-700 font-medium bg-green-50 p-4 rounded-lg\",\n                            children: affirmations[currentAffirmation]\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                            lineNumber: 584,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-gray-500\",\n                            children: isDone ? 'Sesi afirmasi selesai!' : \"Sisa waktu: \".concat(30 - breakTimer, \" detik\")\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                            lineNumber: 587,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                    lineNumber: 582,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_progress__WEBPACK_IMPORTED_MODULE_4__.Progress, {\n                    value: breakTimer / 30 * 100,\n                    className: \"w-64 mx-auto\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                    lineNumber: 591,\n                    columnNumber: 9\n                }, this),\n                isDone && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                    onClick: onBreakComplete,\n                    className: \"bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600\",\n                    children: \"Selesai - Lanjutkan Interview\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                    lineNumber: 594,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n            lineNumber: 578,\n            columnNumber: 7\n        }, this);\n    };\n    _s2(AffirmationBreak, \"1IfL/HKV9L5/h2C8yQtjNxZXuFs=\");\n    // Rest break for general relaxation\n    const RestBreak = ()=>{\n        _s3();\n        const [isDone, setIsDone] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n        (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n            \"PsychologicalSafety.RestBreak.useEffect\": ()=>{\n                if (!isBreakActive) return;\n                const interval = setInterval({\n                    \"PsychologicalSafety.RestBreak.useEffect.interval\": ()=>{\n                        setBreakTimer({\n                            \"PsychologicalSafety.RestBreak.useEffect.interval\": (prev)=>{\n                                const newTimer = prev + 1;\n                                if (newTimer >= 20) {\n                                    setIsBreakActive(false);\n                                    setIsDone(true);\n                                    setIsBreakCompleted(true);\n                                }\n                                return newTimer;\n                            }\n                        }[\"PsychologicalSafety.RestBreak.useEffect.interval\"]);\n                    }\n                }[\"PsychologicalSafety.RestBreak.useEffect.interval\"], 1000);\n                return ({\n                    \"PsychologicalSafety.RestBreak.useEffect\": ()=>clearInterval(interval)\n                })[\"PsychologicalSafety.RestBreak.useEffect\"];\n            }\n        }[\"PsychologicalSafety.RestBreak.useEffect\"], [\n            isBreakActive\n        ]);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-32 h-32 mx-auto rounded-full bg-gradient-to-br from-purple-400 to-indigo-600 flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Coffee_Heart_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        className: \"h-12 w-12 text-white\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                        lineNumber: 630,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                    lineNumber: 629,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-xl font-semibold text-gray-800\",\n                            children: \"Waktu Istirahat Sejenak\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                            lineNumber: 633,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 max-w-md mx-auto\",\n                            children: \"Ambil waktu sejenak untuk rileks. Anda sudah melakukan dengan baik sejauh ini.\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                            lineNumber: 634,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-gray-500\",\n                            children: isDone ? 'Waktu istirahat selesai!' : \"Sisa waktu: \".concat(20 - breakTimer, \" detik\")\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                            lineNumber: 637,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                    lineNumber: 632,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_progress__WEBPACK_IMPORTED_MODULE_4__.Progress, {\n                    value: breakTimer / 20 * 100,\n                    className: \"w-64 mx-auto\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                    lineNumber: 641,\n                    columnNumber: 9\n                }, this),\n                isDone && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                    onClick: onBreakComplete,\n                    className: \"bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600\",\n                    children: \"Selesai - Lanjutkan Interview\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                    lineNumber: 644,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n            lineNumber: 628,\n            columnNumber: 7\n        }, this);\n    };\n    _s3(RestBreak, \"5o5A2HD/StWRXO0HglpKzpDn2bM=\");\n    if (!isVisible) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n            className: \"w-full max-w-2xl\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                    className: \"bg-gradient-to-r from-blue-500 to-purple-500 text-white text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                            className: \"text-2xl font-bold\",\n                            children: \"Psychological Safety Break\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                            lineNumber: 661,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-blue-100\",\n                            children: \"Mari ambil waktu sejenak untuk diri Anda\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                            lineNumber: 662,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                    lineNumber: 660,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                    className: \"p-8\",\n                    children: [\n                        currentPhase === 'pre-test' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PreTestComponent, {}, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                            lineNumber: 665,\n                            columnNumber: 43\n                        }, this),\n                        currentPhase === 'instructions' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(InstructionsComponent, {}, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                            lineNumber: 666,\n                            columnNumber: 47\n                        }, this),\n                        currentPhase === 'breathing' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                breakType === 'breathing' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BreathingBreak, {}, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                    lineNumber: 669,\n                                    columnNumber: 45\n                                }, this),\n                                breakType === 'affirmation' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AffirmationBreak, {}, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                    lineNumber: 670,\n                                    columnNumber: 47\n                                }, this),\n                                breakType === 'rest' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(RestBreak, {}, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                    lineNumber: 671,\n                                    columnNumber: 40\n                                }, this)\n                            ]\n                        }, void 0, true),\n                        currentPhase === 'post-test' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PostTestComponent, {}, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                            lineNumber: 674,\n                            columnNumber: 44\n                        }, this),\n                        currentPhase === 'completed' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CompletedComponent, {}, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                            lineNumber: 675,\n                            columnNumber: 44\n                        }, this),\n                        currentPhase === 'breathing' && !isBreakCompleted && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-8 text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"outline\",\n                                    onClick: onClose,\n                                    className: \"mr-4\",\n                                    children: \"Lewati Break\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                    lineNumber: 679,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    onClick: ()=>{\n                                        setIsBreakActive(false);\n                                        setIsBreakCompleted(true);\n                                    },\n                                    disabled: isBreakActive,\n                                    className: \"bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600\",\n                                    children: isBreakActive ? 'Sedang Break...' : 'Selesaikan Break'\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                    lineNumber: 686,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                            lineNumber: 678,\n                            columnNumber: 13\n                        }, this),\n                        currentPhase === 'breathing' && isBreakCompleted && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-8 text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-4 p-3 bg-green-50 border border-green-200 rounded-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-green-800 font-medium\",\n                                        children: \"✓ Break selesai! Lanjutkan ke evaluasi.\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                        lineNumber: 702,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                    lineNumber: 701,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    onClick: ()=>setCurrentPhase('post-test'),\n                                    className: \"bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600 text-lg px-8 py-3\",\n                                    children: \"Lanjutkan ke Evaluasi\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                    lineNumber: 704,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                            lineNumber: 700,\n                            columnNumber: 13\n                        }, this),\n                        (currentPhase === 'pre-test' || currentPhase === 'instructions') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-8 text-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"outline\",\n                                onClick: onClose,\n                                children: \"Lewati Semua\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                lineNumber: 715,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                            lineNumber: 714,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                    lineNumber: 664,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n            lineNumber: 659,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n        lineNumber: 658,\n        columnNumber: 5\n    }, this);\n}\n_s(PsychologicalSafety, \"T80EamEx/gj3kiCRem+DYCNDD4Q=\");\n_c = PsychologicalSafety;\nvar _c;\n$RefreshReg$(_c, \"PsychologicalSafety\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/psychological-safety.tsx\n"));

/***/ })

});