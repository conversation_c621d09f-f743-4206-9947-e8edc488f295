"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ InterviewPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Bot_Download_Loader2_Mic_MicOff_Play_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Download,Loader2,Mic,MicOff,Play,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Download_Loader2_Mic_MicOff_Play_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Download,Loader2,Mic,MicOff,Play,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Download_Loader2_Mic_MicOff_Play_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Download,Loader2,Mic,MicOff,Play,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Download_Loader2_Mic_MicOff_Play_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Download,Loader2,Mic,MicOff,Play,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Download_Loader2_Mic_MicOff_Play_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Download,Loader2,Mic,MicOff,Play,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Download_Loader2_Mic_MicOff_Play_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Download,Loader2,Mic,MicOff,Play,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/mic-off.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Download_Loader2_Mic_MicOff_Play_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Download,Loader2,Mic,MicOff,Play,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/mic.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_avatar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/avatar */ \"(app-pages-browser)/./components/ui/avatar.tsx\");\n/* harmony import */ var _lib_ai_service__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/ai-service */ \"(app-pages-browser)/./lib/ai-service.ts\");\n/* harmony import */ var _lib_psychological_service__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/psychological-service */ \"(app-pages-browser)/./lib/psychological-service.ts\");\n/* harmony import */ var _components_psychological_assessment__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/psychological-assessment */ \"(app-pages-browser)/./components/psychological-assessment.tsx\");\n/* harmony import */ var _components_psychological_safety__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/psychological-safety */ \"(app-pages-browser)/./components/psychological-safety.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction InterviewPage() {\n    _s();\n    // State untuk menentukan apakah sudah memilih gender atau belum\n    const [hasSelectedGender, setHasSelectedGender] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [voiceGender, setVoiceGender] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('female');\n    // State untuk psychological assessment\n    const [showPsychAssessment, setShowPsychAssessment] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [psychologicalProfile, setPsychologicalProfile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // State untuk psychological safety\n    const [showPsychSafety, setShowPsychSafety] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [stressLevel, setStressLevel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('medium');\n    // State untuk interview\n    const [isRecording, setIsRecording] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isProcessing, setIsProcessing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isInitialized, setIsInitialized] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [recordingTime, setRecordingTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    // Refs untuk audio recording\n    const mediaRecorderRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const audioChunksRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)([]);\n    const timerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Function untuk memilih gender dan mulai psychological assessment\n    const handleGenderSelection = (gender)=>{\n        setVoiceGender(gender);\n        setShowPsychAssessment(true);\n    };\n    // Function untuk skip psychological assessment\n    const handleSkipPsychAssessment = (gender)=>{\n        setVoiceGender(gender);\n        setHasSelectedGender(true);\n    };\n    // Function untuk menyelesaikan psychological assessment\n    const handlePsychAssessmentComplete = (profile)=>{\n        setPsychologicalProfile(profile);\n        setShowPsychAssessment(false);\n        setHasSelectedGender(true);\n    };\n    // Function untuk skip psychological assessment dari dalam component\n    const handlePsychAssessmentSkip = ()=>{\n        setShowPsychAssessment(false);\n        setHasSelectedGender(true);\n    };\n    // Initialize interview setelah gender dipilih\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"InterviewPage.useEffect\": ()=>{\n            if (hasSelectedGender && !isInitialized) {\n                const initializeInterview = {\n                    \"InterviewPage.useEffect.initializeInterview\": async ()=>{\n                        setIsProcessing(true);\n                        try {\n                            console.log(\"Initializing interview...\");\n                            // Generate adaptive prompts based on psychological profile\n                            let initialPrompt = \"Perkenalkan diri Anda sebagai AI interviewer dan mulai wawancara kerja dalam bahasa Indonesia.\";\n                            let welcomeMessage = \"\";\n                            if (psychologicalProfile) {\n                                const adaptivePrompts = (0,_lib_psychological_service__WEBPACK_IMPORTED_MODULE_6__.generateAdaptivePrompts)(psychologicalProfile);\n                                initialPrompt = adaptivePrompts.systemPrompt + \" \" + initialPrompt;\n                                welcomeMessage = adaptivePrompts.welcomeMessage;\n                            }\n                            console.log(\"Sending adaptive initial prompt to AI...\");\n                            const initialResponse = await (0,_lib_ai_service__WEBPACK_IMPORTED_MODULE_5__.processUserInput)(welcomeMessage || initialPrompt, psychologicalProfile || undefined, []);\n                            console.log(\"Received initial AI response:\", initialResponse);\n                            // Convert the initial response to speech\n                            let audioUrl = \"\";\n                            try {\n                                console.log(\"Converting initial response to speech...\");\n                                audioUrl = await (0,_lib_ai_service__WEBPACK_IMPORTED_MODULE_5__.textToSpeech)(initialResponse, voiceGender);\n                            } catch (speechError) {\n                                console.warn(\"Text-to-speech failed, continuing without audio:\", speechError);\n                            }\n                            setMessages([\n                                {\n                                    role: \"assistant\",\n                                    content: initialResponse,\n                                    audioUrl: audioUrl\n                                }\n                            ]);\n                            console.log(\"Interview initialized successfully\");\n                        } catch (error) {\n                            console.error(\"Error initializing interview:\", error);\n                            if (error instanceof Error) {\n                                setError(\"Gagal memulai wawancara: \".concat(error.message, \". Silakan periksa konfigurasi API key.\"));\n                            } else {\n                                setError(\"Gagal memulai wawancara. Silakan muat ulang halaman.\");\n                            }\n                        } finally{\n                            setIsProcessing(false);\n                            setIsInitialized(true);\n                        }\n                    }\n                }[\"InterviewPage.useEffect.initializeInterview\"];\n                initializeInterview();\n            }\n        }\n    }[\"InterviewPage.useEffect\"], [\n        hasSelectedGender,\n        isInitialized,\n        voiceGender\n    ]);\n    // Start the recording timer\n    const startTimer = ()=>{\n        setRecordingTime(0);\n        timerRef.current = setInterval(()=>{\n            setRecordingTime((prevTime)=>prevTime + 1);\n        }, 1000);\n    };\n    // Stop the recording timer\n    const stopTimer = ()=>{\n        if (timerRef.current) {\n            clearInterval(timerRef.current);\n            timerRef.current = null;\n        }\n    };\n    // Start recording using MediaRecorder API\n    const startRecording = async ()=>{\n        setIsRecording(true);\n        audioChunksRef.current = [];\n        try {\n            // Request microphone permission\n            const stream = await navigator.mediaDevices.getUserMedia({\n                audio: true\n            });\n            // Create a new MediaRecorder instance\n            const mediaRecorder = new MediaRecorder(stream);\n            mediaRecorderRef.current = mediaRecorder;\n            // Set up event handlers\n            mediaRecorder.ondataavailable = (event)=>{\n                if (event.data.size > 0) {\n                    audioChunksRef.current.push(event.data);\n                }\n            };\n            // Start recording\n            mediaRecorder.start();\n            startTimer();\n        } catch (error) {\n            console.error(\"Error accessing microphone:\", error);\n            setError(\"Gagal mengakses mikrofon. Pastikan Anda memberikan izin.\");\n            setIsRecording(false);\n        }\n    };\n    // Stop recording and process the audio\n    const stopRecording = async ()=>{\n        setIsRecording(false);\n        stopTimer();\n        if (!mediaRecorderRef.current) {\n            setError(\"Tidak ada rekaman yang sedang berlangsung.\");\n            return;\n        }\n        try {\n            // Create a Promise that resolves when the MediaRecorder stops\n            const recordingData = await new Promise((resolve)=>{\n                mediaRecorderRef.current.onstop = ()=>{\n                    const audioBlob = new Blob(audioChunksRef.current, {\n                        type: 'audio/webm'\n                    });\n                    resolve(audioBlob);\n                };\n                mediaRecorderRef.current.stop();\n            });\n            // Stop all tracks in the stream\n            mediaRecorderRef.current.stream.getTracks().forEach((track)=>track.stop());\n            // Process the recording\n            await processRecording(recordingData);\n        } catch (error) {\n            console.error(\"Error stopping recording:\", error);\n            setError(\"Terjadi kesalahan saat menghentikan rekaman. Silakan coba lagi.\");\n            setIsProcessing(false);\n        }\n    };\n    // Process audio recording\n    const processRecording = async (audioData)=>{\n        setIsProcessing(true);\n        try {\n            console.log(\"Starting to process audio recording...\");\n            // Convert speech to text using Groq Whisper API\n            console.log(\"Converting speech to text with Groq Whisper...\");\n            const transcript = await (0,_lib_ai_service__WEBPACK_IMPORTED_MODULE_5__.speechToText)(audioData);\n            console.log(\"Received transcript:\", transcript);\n            if (!transcript || transcript.trim() === \"\") {\n                throw new Error(\"Tidak ada teks yang terdeteksi dalam rekaman. Silakan coba lagi dengan suara yang lebih jelas.\");\n            }\n            // Add user message\n            const userMessage = {\n                role: \"user\",\n                content: transcript\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    userMessage\n                ]);\n            // Detect stress indicators from user input\n            const stressIndicators = [\n                'um',\n                'eh',\n                'tidak tahu',\n                'mungkin',\n                'sepertinya',\n                'kayaknya',\n                'gimana ya',\n                'bingung',\n                'susah'\n            ];\n            const hasStressIndicators = stressIndicators.some((indicator)=>transcript.toLowerCase().includes(indicator));\n            // Check if user needs psychological safety break\n            if (hasStressIndicators && psychologicalProfile && psychologicalProfile.needsEncouragement && messages.length > 4) {\n                setStressLevel('high');\n                setShowPsychSafety(true);\n                setIsProcessing(false);\n                return;\n            }\n            // Process with AI and get response with psychological adaptation\n            console.log(\"Sending transcript to AI for processing...\");\n            const messageHistoryForAI = messages.map((m)=>({\n                    role: m.role,\n                    content: m.content\n                }));\n            const aiResponse = await (0,_lib_ai_service__WEBPACK_IMPORTED_MODULE_5__.processUserInput)(transcript, psychologicalProfile || undefined, messageHistoryForAI);\n            console.log(\"Received AI response:\", aiResponse);\n            // Convert AI response to speech\n            console.log(\"Converting AI response to speech...\");\n            const audioUrl = await (0,_lib_ai_service__WEBPACK_IMPORTED_MODULE_5__.textToSpeech)(aiResponse, voiceGender);\n            // Add AI message\n            const assistantMessage = {\n                role: \"assistant\",\n                content: aiResponse,\n                audioUrl: audioUrl\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    assistantMessage\n                ]);\n            console.log(\"Processing complete\");\n        } catch (error) {\n            console.error(\"Error processing recording:\", error);\n            if (error instanceof Error) {\n                setError(\"Terjadi kesalahan: \".concat(error.message));\n            } else {\n                setError(\"Terjadi kesalahan saat memproses rekaman. Silakan coba lagi.\");\n            }\n        } finally{\n            setIsProcessing(false);\n        }\n    };\n    // Toggle recording state\n    const toggleRecording = ()=>{\n        if (isRecording) {\n            stopRecording();\n        } else {\n            startRecording();\n        }\n    };\n    // Function to play message text using text-to-speech\n    const playMessage = async (text, audioUrl)=>{\n        try {\n            if (audioUrl) {\n                // If we have a stored audio URL, play it directly\n                const audio = new Audio(audioUrl);\n                audio.play();\n            } else {\n                // Otherwise, generate new speech with current voice gender\n                await (0,_lib_ai_service__WEBPACK_IMPORTED_MODULE_5__.textToSpeech)(text, voiceGender);\n            }\n        } catch (error) {\n            console.error(\"Error playing message:\", error);\n            setError(\"Gagal memutar pesan. Silakan coba lagi.\");\n        }\n    };\n    // Function to export the transcript\n    const exportTranscript = ()=>{\n        try {\n            // Format the transcript\n            const date = new Date().toLocaleString(\"id-ID\");\n            let transcriptContent = \"AI Interview Transcript - \".concat(date, \"\\n\\n\");\n            messages.forEach((message)=>{\n                const role = message.role === \"assistant\" ? \"AI Interviewer\" : \"Kandidat\";\n                transcriptContent += \"\".concat(role, \": \").concat(message.content, \"\\n\\n\");\n            });\n            // Create a blob and download link\n            const blob = new Blob([\n                transcriptContent\n            ], {\n                type: \"text/plain;charset=utf-8\"\n            });\n            const url = URL.createObjectURL(blob);\n            // Create a temporary link and trigger download\n            const link = document.createElement(\"a\");\n            link.href = url;\n            link.download = \"interview-transcript-\".concat(new Date().toISOString().slice(0, 10), \".txt\");\n            document.body.appendChild(link);\n            link.click();\n            // Clean up\n            document.body.removeChild(link);\n            URL.revokeObjectURL(url);\n        } catch (error) {\n            console.error(\"Error exporting transcript:\", error);\n            setError(\"Gagal mengekspor transkrip. Silakan coba lagi.\");\n        }\n    };\n    // Jika sedang menampilkan psychological assessment\n    if (showPsychAssessment) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_psychological_assessment__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n            onComplete: handlePsychAssessmentComplete,\n            onSkip: handlePsychAssessmentSkip\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n            lineNumber: 332,\n            columnNumber: 12\n        }, this);\n    }\n    // Jika belum memilih gender, tampilkan halaman pilihan\n    if (!hasSelectedGender) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 flex items-center justify-center p-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full max-w-4xl\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"inline-flex items-center justify-center w-20 h-20 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-full mb-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Download_Loader2_Mic_MicOff_Play_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"h-10 w-10 text-white\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                    lineNumber: 343,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                lineNumber: 342,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-4xl font-bold text-gray-900 mb-4\",\n                                children: \"AI Interview System\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                lineNumber: 345,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl text-gray-600 mb-2\",\n                                children: \"Sistem Wawancara Kerja dengan Kecerdasan Buatan\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                lineNumber: 346,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-500\",\n                                children: \"Pilih suara AI interviewer dan metode persiapan yang Anda inginkan\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                lineNumber: 347,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                        lineNumber: 341,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 gap-8 max-w-3xl mx-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                className: \"group transition-all duration-300 hover:shadow-2xl hover:scale-105 border-0 shadow-lg bg-white/80 backdrop-blur-sm\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    className: \"p-8 text-center h-full flex flex-col justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-24 h-24 mx-auto mb-6 bg-gradient-to-br from-pink-100 to-rose-100 rounded-full flex items-center justify-center group-hover:from-pink-200 group-hover:to-rose-200 transition-all duration-300\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-4xl\",\n                                                        children: \"\\uD83D\\uDC69‍\\uD83D\\uDCBC\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                        lineNumber: 359,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                    lineNumber: 358,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-2xl font-bold text-gray-900 mb-3\",\n                                                    children: \"Suara Perempuan\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                    lineNumber: 361,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600 mb-4 leading-relaxed\",\n                                                    children: \"Suara perempuan Indonesia yang natural dan profesional dari Google TTS\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                    lineNumber: 362,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-center text-sm text-gray-500 mb-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-green-400 rounded-full mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                            lineNumber: 366,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"Google TTS Indonesia\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                    lineNumber: 365,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                            lineNumber: 357,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    className: \"w-full bg-gradient-to-r from-pink-500 to-rose-500 hover:from-pink-600 hover:to-rose-600 text-white font-semibold py-3 rounded-xl shadow-lg transition-all duration-300\",\n                                                    onClick: ()=>handleGenderSelection('female'),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Download_Loader2_Mic_MicOff_Play_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                            className: \"h-5 w-5 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                            lineNumber: 375,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"Pilih dengan Persiapan Psikologis\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                    lineNumber: 371,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    variant: \"outline\",\n                                                    className: \"w-full border-pink-300 text-pink-600 hover:bg-pink-50 py-2 rounded-xl transition-all duration-300\",\n                                                    onClick: ()=>handleSkipPsychAssessment('female'),\n                                                    children: \"Langsung Mulai Interview\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                    lineNumber: 378,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                            lineNumber: 370,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                    lineNumber: 356,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                lineNumber: 353,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                className: \"group transition-all duration-300 hover:shadow-2xl hover:scale-105 border-0 shadow-lg bg-white/80 backdrop-blur-sm\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    className: \"p-8 text-center h-full flex flex-col justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-24 h-24 mx-auto mb-6 bg-gradient-to-br from-blue-100 to-indigo-100 rounded-full flex items-center justify-center group-hover:from-blue-200 group-hover:to-indigo-200 transition-all duration-300\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-4xl\",\n                                                        children: \"\\uD83D\\uDC68‍\\uD83D\\uDCBC\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                        lineNumber: 396,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                    lineNumber: 395,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-2xl font-bold text-gray-900 mb-3\",\n                                                    children: \"Suara Laki-laki\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                    lineNumber: 398,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600 mb-4 leading-relaxed\",\n                                                    children: \"Suara laki-laki Indonesia yang natural dan profesional dari Google TTS\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                    lineNumber: 399,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-center text-sm text-gray-500 mb-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-green-400 rounded-full mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                            lineNumber: 403,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"Google TTS Indonesia\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                    lineNumber: 402,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                            lineNumber: 394,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    className: \"w-full bg-gradient-to-r from-blue-500 to-indigo-500 hover:from-blue-600 hover:to-indigo-600 text-white font-semibold py-3 rounded-xl shadow-lg transition-all duration-300\",\n                                                    onClick: ()=>handleGenderSelection('male'),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Download_Loader2_Mic_MicOff_Play_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                            className: \"h-5 w-5 mr-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                            lineNumber: 412,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"Pilih dengan Persiapan Psikologis\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                    lineNumber: 408,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    variant: \"outline\",\n                                                    className: \"w-full border-blue-300 text-blue-600 hover:bg-blue-50 py-2 rounded-xl transition-all duration-300\",\n                                                    onClick: ()=>handleSkipPsychAssessment('male'),\n                                                    children: \"Langsung Mulai Interview\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                    lineNumber: 415,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                            lineNumber: 407,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                    lineNumber: 393,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                lineNumber: 390,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                        lineNumber: 351,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mt-12\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-gray-500\",\n                            children: \"Powered by Google TTS • Groq Whisper • Gemini AI\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                            lineNumber: 429,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                        lineNumber: 428,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                lineNumber: 339,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n            lineNumber: 338,\n            columnNumber: 7\n        }, this);\n    }\n    // Interface wawancara dengan layout kiri-kanan\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_psychological_safety__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                isVisible: showPsychSafety,\n                onClose: ()=>setShowPsychSafety(false),\n                onBreakComplete: ()=>setShowPsychSafety(false),\n                profile: psychologicalProfile || undefined,\n                stressLevel: stressLevel\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                lineNumber: 442,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-r from-blue-600 to-indigo-600 text-white p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto flex justify-between items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-2xl font-bold\",\n                                    children: \"AI Interview System\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                    lineNumber: 453,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-blue-100\",\n                                    children: [\n                                        \"Suara: \",\n                                        _lib_ai_service__WEBPACK_IMPORTED_MODULE_5__.VOICE_OPTIONS[voiceGender].name\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                    lineNumber: 454,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                            lineNumber: 452,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    onClick: exportTranscript,\n                                    disabled: messages.length === 0,\n                                    className: \"bg-white/10 border-white/20 text-white hover:bg-white/20\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Download_Loader2_Mic_MicOff_Play_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"h-4 w-4 mr-1\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                            lineNumber: 464,\n                                            columnNumber: 15\n                                        }, this),\n                                        \" Export\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                    lineNumber: 457,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    onClick: ()=>setHasSelectedGender(false),\n                                    className: \"bg-white/10 border-white/20 text-white hover:bg-white/20\",\n                                    children: \"Ganti Suara\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                    lineNumber: 466,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                            lineNumber: 456,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                    lineNumber: 451,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                lineNumber: 450,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto p-4 h-[calc(100vh-80px)] flex flex-col\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        className: \"h-80 mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                className: \"bg-gray-50 py-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                    className: \"text-lg\",\n                                    children: \"Percakapan Interview\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                    lineNumber: 483,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                lineNumber: 482,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                className: \"p-4 h-72 overflow-y-auto flex flex-col-reverse\",\n                                children: [\n                                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: error\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                lineNumber: 488,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                variant: \"outline\",\n                                                size: \"sm\",\n                                                className: \"mt-2\",\n                                                onClick: ()=>setError(null),\n                                                children: \"Tutup\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                lineNumber: 489,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                        lineNumber: 487,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4 flex flex-col-reverse\",\n                                        children: [\n                                            isProcessing && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-center py-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Download_Loader2_Mic_MicOff_Play_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"h-6 w-6 animate-spin text-blue-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                        lineNumber: 498,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ml-2 text-sm text-gray-600\",\n                                                        children: \"AI sedang memproses...\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                        lineNumber: 499,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                lineNumber: 497,\n                                                columnNumber: 17\n                                            }, this),\n                                            messages.slice().reverse().map((message, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex \".concat(message.role === \"assistant\" ? \"justify-start\" : \"justify-end\"),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex gap-3 max-w-[85%] \".concat(message.role === \"assistant\" ? \"flex-row\" : \"flex-row-reverse\"),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_4__.Avatar, {\n                                                                className: \"w-10 h-10 \".concat(message.role === \"assistant\" ? \"bg-blue-100\" : \"bg-green-100\"),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_4__.AvatarFallback, {\n                                                                    children: message.role === \"assistant\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Download_Loader2_Mic_MicOff_Play_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                        className: \"h-5 w-5 text-blue-500\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                                        lineNumber: 511,\n                                                                        columnNumber: 27\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Download_Loader2_Mic_MicOff_Play_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                        className: \"h-5 w-5 text-green-500\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                                        lineNumber: 513,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                                    lineNumber: 509,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                                lineNumber: 508,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"rounded-lg p-4 text-sm shadow-sm \".concat(message.role === \"assistant\" ? \"bg-blue-50 border-l-4 border-blue-400\" : \"bg-green-50 border-l-4 border-green-400\"),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-xs font-medium mb-1 \".concat(message.role === \"assistant\" ? \"text-blue-600\" : \"text-green-600\"),\n                                                                        children: message.role === \"assistant\" ? \"AI Interviewer\" : \"Anda\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                                        lineNumber: 518,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-gray-800 leading-relaxed\",\n                                                                        children: message.content\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                                        lineNumber: 521,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    message.role === \"assistant\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                        variant: \"ghost\",\n                                                                        size: \"sm\",\n                                                                        className: \"mt-2 h-7 px-3 text-xs hover:bg-blue-100\",\n                                                                        onClick: ()=>playMessage(message.content, message.audioUrl),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Download_Loader2_Mic_MicOff_Play_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                                className: \"h-3 w-3 mr-1\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                                                lineNumber: 529,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            \" Putar Ulang\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                                        lineNumber: 523,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                                lineNumber: 517,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                        lineNumber: 505,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, messages.length - 1 - index, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                    lineNumber: 504,\n                                                    columnNumber: 17\n                                                }, this)),\n                                            messages.length === 0 && !isProcessing && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center text-gray-500 py-12\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Download_Loader2_Mic_MicOff_Play_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"h-12 w-12 mx-auto mb-4 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                        lineNumber: 539,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-lg font-medium\",\n                                                        children: isInitialized ? \"Mulai berbicara dengan menekan tombol mikrofon di bawah\" : \"Memulai wawancara...\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                        lineNumber: 540,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm mt-2\",\n                                                        children: \"Percakapan terbaru akan muncul di atas\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                        lineNumber: 543,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                lineNumber: 538,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                        lineNumber: 495,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                lineNumber: 485,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                        lineNumber: 481,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 gap-6 flex-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                className: \"flex flex-col\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                        className: \"bg-blue-50 text-center py-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                            className: \"text-base\",\n                                            children: \"AI Interviewer\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                            lineNumber: 555,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                        lineNumber: 554,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                        className: \"flex-1 flex flex-col items-center justify-center p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_4__.Avatar, {\n                                                className: \"w-24 h-24 mb-3 bg-blue-100\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_4__.AvatarFallback, {\n                                                    className: \"text-3xl\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Download_Loader2_Mic_MicOff_Play_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"h-12 w-12 text-blue-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                        lineNumber: 560,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                    lineNumber: 559,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                lineNumber: 558,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-center text-sm text-gray-600\",\n                                                children: _lib_ai_service__WEBPACK_IMPORTED_MODULE_5__.VOICE_OPTIONS[voiceGender].name\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                lineNumber: 563,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-3 h-3 rounded-full mt-2 \".concat(isProcessing ? \"bg-yellow-400 animate-pulse\" : \"bg-green-400\")\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                lineNumber: 566,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                        lineNumber: 557,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                lineNumber: 553,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                className: \"flex flex-col\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                        className: \"bg-green-50 text-center py-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                            className: \"text-base\",\n                                            children: \"Kandidat\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                            lineNumber: 573,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                        lineNumber: 572,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                        className: \"flex-1 flex flex-col items-center justify-center p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_4__.Avatar, {\n                                                className: \"w-24 h-24 mb-3 bg-green-100\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_4__.AvatarFallback, {\n                                                    className: \"text-3xl\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Download_Loader2_Mic_MicOff_Play_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"h-12 w-12 text-green-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                        lineNumber: 578,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                    lineNumber: 577,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                lineNumber: 576,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        onClick: toggleRecording,\n                                                        disabled: isProcessing,\n                                                        size: \"lg\",\n                                                        className: \"rounded-full h-14 w-14 mb-2 shadow-lg transition-all duration-200 \".concat(isRecording ? \"bg-red-500 hover:bg-red-600 animate-pulse\" : \"bg-green-500 hover:bg-green-600 hover:scale-105\"),\n                                                        children: isRecording ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Download_Loader2_Mic_MicOff_Play_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                            className: \"h-7 w-7\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                            lineNumber: 592,\n                                                            columnNumber: 34\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Download_Loader2_Mic_MicOff_Play_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            className: \"h-7 w-7\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                            lineNumber: 592,\n                                                            columnNumber: 67\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                        lineNumber: 582,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-600\",\n                                                        children: isProcessing ? \"Memproses...\" : isRecording ? \"Merekam... (\".concat(recordingTime, \"s)\") : \"Klik untuk berbicara\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                        lineNumber: 594,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-3 h-3 rounded-full mt-2 mx-auto \".concat(isRecording ? \"bg-red-400 animate-pulse\" : isProcessing ? \"bg-yellow-400 animate-pulse\" : \"bg-gray-300\")\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                        lineNumber: 601,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                                lineNumber: 581,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                        lineNumber: 575,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                                lineNumber: 571,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                        lineNumber: 551,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n                lineNumber: 479,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/app/page.tsx\",\n        lineNumber: 440,\n        columnNumber: 5\n    }, this);\n}\n_s(InterviewPage, \"i/xoenAARJsLuEZRl14rl5wKxwI=\");\n_c = InterviewPage;\nvar _c;\n$RefreshReg$(_c, \"InterviewPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/page.tsx\n"));

/***/ })

});