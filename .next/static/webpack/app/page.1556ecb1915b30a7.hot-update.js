"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./components/psychological-safety.tsx":
/*!*********************************************!*\
  !*** ./components/psychological-safety.tsx ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PsychologicalSafety)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_progress__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/progress */ \"(app-pages-browser)/./components/ui/progress.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./components/ui/textarea.tsx\");\n/* harmony import */ var _barrel_optimize_names_Coffee_Heart_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Coffee,Heart,Shield!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_Coffee_Heart_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Coffee,Heart,Shield!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Coffee_Heart_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Coffee,Heart,Shield!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/coffee.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction PsychologicalSafety(param) {\n    let { isVisible, onClose, onBreakComplete, profile, stressLevel = 'medium' } = param;\n    _s();\n    var _s1 = $RefreshSig$(), _s2 = $RefreshSig$(), _s3 = $RefreshSig$();\n    const [breakType, setBreakType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('breathing');\n    const [breakTimer, setBreakTimer] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [isBreakActive, setIsBreakActive] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isBreakCompleted, setIsBreakCompleted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [breathingPhase, setBreathingPhase] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('inhale');\n    // Pre-post test states\n    const [currentPhase, setCurrentPhase] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('pre-test');\n    const [preTestData, setPreTestData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        feeling: '',\n        score: 5\n    });\n    const [postTestData, setPostTestData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        feeling: '',\n        score: 5\n    });\n    // Auto-start break when component becomes visible\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PsychologicalSafety.useEffect\": ()=>{\n            if (isVisible && currentPhase === 'breathing' && !isBreakActive) {\n                startBreak();\n            }\n        }\n    }[\"PsychologicalSafety.useEffect\"], [\n        isVisible,\n        currentPhase\n    ]);\n    const startBreak = ()=>{\n        setIsBreakActive(true);\n        setBreakTimer(0);\n        // Choose break type based on stress level and profile\n        if (stressLevel === 'high' || profile && profile.anxietyLevel === 'high') {\n            setBreakType('breathing');\n        } else if (profile && profile.needsEncouragement) {\n            setBreakType('affirmation');\n        } else {\n            setBreakType('rest');\n        }\n    };\n    // Pre-test component\n    const PreTestComponent = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-32 h-32 mx-auto rounded-full bg-gradient-to-br from-blue-400 to-blue-600 flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-white text-4xl\",\n                        children: \"\\uD83D\\uDCDD\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                    lineNumber: 68,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-2xl font-semibold text-gray-800\",\n                            children: \"Sebelum Latihan Pernapasan\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                            lineNumber: 72,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: \"Mari kita ukur perasaan Anda saat ini\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                            lineNumber: 73,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-50 p-6 rounded-lg space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Bagaimana perasaan Anda saat ini?\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                            lineNumber: 77,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_5__.Textarea, {\n                                            value: preTestData.feeling,\n                                            onChange: (e)=>setPreTestData((prev)=>({\n                                                        ...prev,\n                                                        feeling: e.target.value\n                                                    })),\n                                            placeholder: \"Contoh: Saya merasa cemas dan gugup...\",\n                                            className: \"resize-none h-20\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                            lineNumber: 80,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                    lineNumber: 76,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Seberapa tenang perasaan Anda? (1 = Sangat Cemas, 10 = Sangat Tenang)\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                            lineNumber: 89,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-gray-500\",\n                                                    children: \"1\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                                    lineNumber: 93,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"range\",\n                                                    min: \"1\",\n                                                    max: \"10\",\n                                                    value: preTestData.score,\n                                                    onChange: (e)=>setPreTestData((prev)=>({\n                                                                ...prev,\n                                                                score: parseInt(e.target.value)\n                                                            })),\n                                                    className: \"flex-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                                    lineNumber: 94,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-gray-500\",\n                                                    children: \"10\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                                    lineNumber: 102,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                            lineNumber: 92,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center mt-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-2xl font-bold text-blue-600\",\n                                                children: preTestData.score\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                                lineNumber: 105,\n                                                columnNumber: 15\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                            lineNumber: 104,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                    lineNumber: 88,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                            lineNumber: 75,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            onClick: ()=>setCurrentPhase('instructions'),\n                            disabled: !preTestData.feeling.trim(),\n                            className: \"bg-gradient-to-r from-blue-500 to-indigo-500 hover:from-blue-600 hover:to-indigo-600\",\n                            children: \"Lanjutkan ke Instruksi\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                            lineNumber: 110,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                    lineNumber: 71,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n            lineNumber: 67,\n            columnNumber: 5\n        }, this);\n    // Instructions component with visual grid\n    const InstructionsComponent = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-32 h-32 mx-auto rounded-full bg-gradient-to-br from-green-400 to-emerald-600 flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-white text-4xl\",\n                        children: \"\\uD83D\\uDCCB\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                        lineNumber: 125,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                    lineNumber: 124,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-2xl font-semibold text-gray-800\",\n                            children: \"Instruksi Latihan Pernapasan\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                            lineNumber: 128,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: \"Ikuti pola pernapasan berikut untuk merasa lebih tenang\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                            lineNumber: 129,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-50 p-6 rounded-lg space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-600 mb-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Pola Pernapasan 4-3-4:\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                        lineNumber: 133,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                    lineNumber: 132,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-4 gap-2 w-48 mx-auto mb-4\",\n                                    children: Array.from({\n                                        length: 12\n                                    }, (_, i)=>{\n                                        let squareColor = 'bg-gray-200';\n                                        let label = '';\n                                        if (i < 4) {\n                                            squareColor = 'bg-blue-500';\n                                            label = 'T';\n                                        } else if (i < 7) {\n                                            squareColor = 'bg-yellow-500';\n                                            label = 'H';\n                                        } else if (i < 11) {\n                                            squareColor = 'bg-green-500';\n                                            label = 'B';\n                                        } else {\n                                            squareColor = 'bg-gray-300';\n                                            label = '↻';\n                                        }\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-10 h-10 \".concat(squareColor, \" rounded flex items-center justify-center text-white font-bold text-sm\"),\n                                            children: label\n                                        }, i, false, {\n                                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                            lineNumber: 157,\n                                            columnNumber: 17\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                    lineNumber: 137,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2 text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-4 h-4 bg-blue-500 rounded\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                                    lineNumber: 169,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"T (Tarik):\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                                            lineNumber: 170,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \" Tarik napas dalam-dalam selama 4 detik\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                                    lineNumber: 170,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                            lineNumber: 168,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-4 h-4 bg-yellow-500 rounded\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                                    lineNumber: 173,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"H (Tahan):\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                                            lineNumber: 174,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \" Tahan napas selama 3 detik\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                                    lineNumber: 174,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                            lineNumber: 172,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-4 h-4 bg-green-500 rounded\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                                    lineNumber: 177,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"B (Buang):\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                                            lineNumber: 178,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \" Hembuskan napas perlahan selama 4 detik\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                                    lineNumber: 178,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                            lineNumber: 176,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                    lineNumber: 167,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                            lineNumber: 131,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            onClick: ()=>setCurrentPhase('breathing'),\n                            className: \"bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600\",\n                            children: \"Mulai Latihan Pernapasan\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                            lineNumber: 183,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                    lineNumber: 127,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n            lineNumber: 123,\n            columnNumber: 5\n        }, this);\n    // Post-test component\n    const PostTestComponent = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-32 h-32 mx-auto rounded-full bg-gradient-to-br from-purple-400 to-purple-600 flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-white text-4xl\",\n                        children: \"✅\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                        lineNumber: 197,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                    lineNumber: 196,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-2xl font-semibold text-gray-800\",\n                            children: \"Setelah Latihan Pernapasan\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                            lineNumber: 200,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: \"Bagaimana perasaan Anda sekarang?\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                            lineNumber: 201,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-50 p-6 rounded-lg space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Bagaimana perasaan Anda sekarang?\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                            lineNumber: 205,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_5__.Textarea, {\n                                            value: postTestData.feeling,\n                                            onChange: (e)=>setPostTestData((prev)=>({\n                                                        ...prev,\n                                                        feeling: e.target.value\n                                                    })),\n                                            placeholder: \"Contoh: Saya merasa lebih tenang dan rileks...\",\n                                            className: \"resize-none h-20\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                            lineNumber: 208,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                    lineNumber: 204,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Seberapa tenang perasaan Anda sekarang? (1 = Sangat Cemas, 10 = Sangat Tenang)\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                            lineNumber: 217,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-gray-500\",\n                                                    children: \"1\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                                    lineNumber: 221,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"range\",\n                                                    min: \"1\",\n                                                    max: \"10\",\n                                                    value: postTestData.score,\n                                                    onChange: (e)=>setPostTestData((prev)=>({\n                                                                ...prev,\n                                                                score: parseInt(e.target.value)\n                                                            })),\n                                                    className: \"flex-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                                    lineNumber: 222,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-gray-500\",\n                                                    children: \"10\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                                    lineNumber: 230,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                            lineNumber: 220,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center mt-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-2xl font-bold text-purple-600\",\n                                                children: postTestData.score\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                                lineNumber: 233,\n                                                columnNumber: 15\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                            lineNumber: 232,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                    lineNumber: 216,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-blue-50 p-4 rounded-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-medium text-blue-800 mb-2\",\n                                            children: \"Perbandingan:\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                            lineNumber: 239,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm space-y-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        \"Sebelum: \",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-bold\",\n                                                            children: [\n                                                                preTestData.score,\n                                                                \"/10\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                                            lineNumber: 241,\n                                                            columnNumber: 29\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                                    lineNumber: 241,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        \"Sekarang: \",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-bold\",\n                                                            children: [\n                                                                postTestData.score,\n                                                                \"/10\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                                            lineNumber: 242,\n                                                            columnNumber: 30\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                                    lineNumber: 242,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"font-bold \".concat(postTestData.score > preTestData.score ? 'text-green-600' : postTestData.score < preTestData.score ? 'text-red-600' : 'text-gray-600'),\n                                                    children: [\n                                                        \"Perubahan: \",\n                                                        postTestData.score > preTestData.score ? '+' : '',\n                                                        postTestData.score - preTestData.score\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                                    lineNumber: 243,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                            lineNumber: 240,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                    lineNumber: 238,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                            lineNumber: 203,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            onClick: ()=>setCurrentPhase('completed'),\n                            disabled: !postTestData.feeling.trim(),\n                            className: \"bg-gradient-to-r from-purple-500 to-indigo-500 hover:from-purple-600 hover:to-indigo-600\",\n                            children: \"Selesai\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                            lineNumber: 250,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                    lineNumber: 199,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n            lineNumber: 195,\n            columnNumber: 5\n        }, this);\n    // Completed component\n    const CompletedComponent = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-32 h-32 mx-auto rounded-full bg-gradient-to-br from-green-400 to-emerald-600 flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-white text-4xl\",\n                        children: \"\\uD83C\\uDF89\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                        lineNumber: 265,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                    lineNumber: 264,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-2xl font-semibold text-gray-800\",\n                            children: \"Latihan Pernapasan Selesai!\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                            lineNumber: 268,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: \"Terima kasih telah mengikuti latihan pernapasan\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                            lineNumber: 269,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gradient-to-r from-green-50 to-emerald-50 p-6 rounded-lg space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"font-semibold text-green-800\",\n                                    children: \"Ringkasan Hasil:\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                    lineNumber: 272,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-2 gap-4 text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white p-3 rounded\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-gray-600\",\n                                                    children: \"Sebelum Latihan\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                                    lineNumber: 275,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl font-bold text-blue-600\",\n                                                    children: [\n                                                        preTestData.score,\n                                                        \"/10\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                                    lineNumber: 276,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                            lineNumber: 274,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white p-3 rounded\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-gray-600\",\n                                                    children: \"Setelah Latihan\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                                    lineNumber: 279,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl font-bold text-purple-600\",\n                                                    children: [\n                                                        postTestData.score,\n                                                        \"/10\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                                    lineNumber: 280,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                            lineNumber: 278,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                    lineNumber: 273,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center p-3 rounded font-bold \".concat(postTestData.score > preTestData.score ? 'bg-green-100 text-green-800' : postTestData.score < preTestData.score ? 'bg-red-100 text-red-800' : 'bg-gray-100 text-gray-800'),\n                                    children: postTestData.score > preTestData.score ? '✅ Perasaan Anda membaik!' : postTestData.score < preTestData.score ? '⚠️ Mungkin perlu latihan lebih lanjut' : '➡️ Perasaan Anda stabil'\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                    lineNumber: 283,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                            lineNumber: 271,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            onClick: onBreakComplete,\n                            className: \"bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600 text-lg px-8 py-3\",\n                            children: \"Mulai Interview\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                            lineNumber: 288,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                    lineNumber: 267,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n            lineNumber: 263,\n            columnNumber: 5\n        }, this);\n    // Breathing exercise for high stress\n    const BreathingBreak = ()=>{\n        _s1();\n        const [count, setCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n        const [cycleCount, setCycleCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n        const [isDone, setIsDone] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n        const [isStarted, setIsStarted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n        const [totalTimer, setTotalTimer] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n        const TOTAL_CYCLES = 3;\n        const CYCLE_DURATION = 10 // 4 + 2 + 4 seconds\n        ;\n        (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n            \"PsychologicalSafety.BreathingBreak.useEffect\": ()=>{\n                if (!isBreakActive || !isStarted) return;\n                const interval = setInterval({\n                    \"PsychologicalSafety.BreathingBreak.useEffect.interval\": ()=>{\n                        setCount({\n                            \"PsychologicalSafety.BreathingBreak.useEffect.interval\": (prev)=>{\n                                const newCount = prev + 1;\n                                setTotalTimer({\n                                    \"PsychologicalSafety.BreathingBreak.useEffect.interval\": (prevTotal)=>prevTotal + 1\n                                }[\"PsychologicalSafety.BreathingBreak.useEffect.interval\"]);\n                                if (newCount <= 4) {\n                                    setBreathingPhase('inhale');\n                                } else if (newCount <= 6) {\n                                    setBreathingPhase('hold');\n                                } else if (newCount <= 10) {\n                                    setBreathingPhase('exhale');\n                                } else {\n                                    // Complete one cycle\n                                    setCycleCount({\n                                        \"PsychologicalSafety.BreathingBreak.useEffect.interval\": (prevCycle)=>{\n                                            const newCycleCount = prevCycle + 1;\n                                            // Check if we've completed all cycles\n                                            if (newCycleCount >= TOTAL_CYCLES) {\n                                                setIsBreakActive(false);\n                                                setIsDone(true);\n                                                setIsBreakCompleted(true);\n                                            }\n                                            return newCycleCount;\n                                        }\n                                    }[\"PsychologicalSafety.BreathingBreak.useEffect.interval\"]);\n                                    setCount(0);\n                                    return 0;\n                                }\n                                return newCount;\n                            }\n                        }[\"PsychologicalSafety.BreathingBreak.useEffect.interval\"]);\n                    }\n                }[\"PsychologicalSafety.BreathingBreak.useEffect.interval\"], 1000);\n                return ({\n                    \"PsychologicalSafety.BreathingBreak.useEffect\": ()=>clearInterval(interval)\n                })[\"PsychologicalSafety.BreathingBreak.useEffect\"];\n            }\n        }[\"PsychologicalSafety.BreathingBreak.useEffect\"], [\n            isBreakActive,\n            isStarted\n        ]);\n        const getBreathingInstruction = ()=>{\n            switch(breathingPhase){\n                case 'inhale':\n                    return 'Tarik napas dalam-dalam...';\n                case 'hold':\n                    return 'Tahan napas...';\n                case 'exhale':\n                    return 'Hembuskan perlahan...';\n            }\n        };\n        const getBreathingColor = ()=>{\n            switch(breathingPhase){\n                case 'inhale':\n                    return 'from-blue-400 to-blue-600';\n                case 'hold':\n                    return 'from-yellow-400 to-yellow-600';\n                case 'exhale':\n                    return 'from-green-400 to-green-600';\n            }\n        };\n        const getPhaseSeconds = ()=>{\n            switch(breathingPhase){\n                case 'inhale':\n                    return 4;\n                case 'hold':\n                    return 2;\n                case 'exhale':\n                    return 4;\n            }\n        };\n        const getCurrentPhaseProgress = ()=>{\n            const phaseStart = breathingPhase === 'inhale' ? 0 : breathingPhase === 'hold' ? 4 : 6;\n            const currentInPhase = count - phaseStart;\n            return Math.min(currentInPhase, getPhaseSeconds());\n        };\n        const getPhaseIcon = ()=>{\n            switch(breathingPhase){\n                case 'inhale':\n                    return '↑';\n                case 'hold':\n                    return '⏸';\n                case 'exhale':\n                    return '↓';\n                default:\n                    return '○';\n            }\n        };\n        const resetBreathingExercise = ()=>{\n            setCount(0);\n            setCycleCount(0);\n            setIsDone(false);\n            setTotalTimer(0);\n            setIsBreakActive(true);\n            setBreathingPhase('inhale');\n        };\n        // Modern breathing pattern cards\n        const BreathingCards = ()=>{\n            const phases = [\n                {\n                    name: 'Tarik',\n                    duration: 4,\n                    color: 'bg-blue-500',\n                    textColor: 'text-blue-600',\n                    bgColor: 'bg-blue-50',\n                    icon: '↑'\n                },\n                {\n                    name: 'Tahan',\n                    duration: 2,\n                    color: 'bg-yellow-500',\n                    textColor: 'text-yellow-600',\n                    bgColor: 'bg-yellow-50',\n                    icon: '⏸'\n                },\n                {\n                    name: 'Hembus',\n                    duration: 4,\n                    color: 'bg-green-500',\n                    textColor: 'text-green-600',\n                    bgColor: 'bg-green-50',\n                    icon: '↓'\n                }\n            ];\n            const getCurrentPhaseIndex = ()=>{\n                if (breathingPhase === 'inhale') return 0;\n                if (breathingPhase === 'hold') return 1;\n                if (breathingPhase === 'exhale') return 2;\n                return -1;\n            };\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-2 gap-4 max-w-md mx-auto mb-6\",\n                children: phases.map((phase, index)=>{\n                    const isActive = getCurrentPhaseIndex() === index;\n                    const isCompleted = getCurrentPhaseIndex() > index;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative p-4 rounded-xl border-2 transition-all duration-500 \".concat(isActive ? \"\".concat(phase.bgColor, \" border-\").concat(phase.color.replace('bg-', ''), \" shadow-lg scale-105\") : isCompleted ? 'bg-gray-100 border-gray-300' : 'bg-white border-gray-200'),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl mb-1 \".concat(isActive ? phase.textColor : 'text-gray-400'),\n                                        children: phase.icon\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                        lineNumber: 429,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"font-semibold text-lg \".concat(isActive ? phase.textColor : 'text-gray-600'),\n                                        children: phase.name\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                        lineNumber: 432,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm \".concat(isActive ? phase.textColor : 'text-gray-500'),\n                                        children: [\n                                            phase.duration,\n                                            \" detik\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                        lineNumber: 435,\n                                        columnNumber: 19\n                                    }, this),\n                                    isActive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-2 w-8 h-8 mx-auto rounded-full \".concat(phase.color, \" flex items-center justify-center text-white font-bold animate-pulse\"),\n                                        children: getCurrentPhaseProgress()\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                        lineNumber: 439,\n                                        columnNumber: 21\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                lineNumber: 428,\n                                columnNumber: 17\n                            }, this),\n                            isCompleted && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-2 right-2 w-5 h-5 bg-green-500 rounded-full flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-white text-xs\",\n                                    children: \"✓\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                    lineNumber: 446,\n                                    columnNumber: 21\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                lineNumber: 445,\n                                columnNumber: 19\n                            }, this)\n                        ]\n                    }, phase.name, true, {\n                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                        lineNumber: 418,\n                        columnNumber: 15\n                    }, this);\n                })\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                lineNumber: 412,\n                columnNumber: 9\n            }, this);\n        };\n        // Show instructions first, then breathing exercise\n        if (!isStarted) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center space-y-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-20 h-20 mx-auto bg-gradient-to-br from-pink-400 to-red-500 rounded-full flex items-center justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Coffee_Heart_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            className: \"h-10 w-10 text-white\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                            lineNumber: 462,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                        lineNumber: 461,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold text-gray-800\",\n                                children: \"Latihan Pernapasan\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                lineNumber: 467,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 max-w-md mx-auto\",\n                                children: \"Mari kita mulai dengan latihan pernapasan singkat untuk membantu Anda merasa lebih tenang dan fokus.\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                lineNumber: 468,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-500\",\n                                children: \"Hanya 3 siklus (30 detik) - Anda bisa mengulang jika diperlukan\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                lineNumber: 471,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                        lineNumber: 466,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BreathingCards, {}, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                        lineNumber: 477,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-blue-50 p-4 rounded-xl border border-blue-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-blue-800 font-medium\",\n                                children: [\n                                    \"Siklus 1 dari \",\n                                    TOTAL_CYCLES\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                lineNumber: 481,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-blue-600 text-sm mt-1\",\n                                children: [\n                                    \"Total durasi: \",\n                                    TOTAL_CYCLES * CYCLE_DURATION,\n                                    \" detik (30 detik)\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                lineNumber: 482,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                        lineNumber: 480,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                        onClick: ()=>setIsStarted(true),\n                        className: \"bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 text-lg px-8 py-3 rounded-xl\",\n                        children: \"Mulai Latihan\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                        lineNumber: 486,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                        variant: \"outline\",\n                        onClick: ()=>setCurrentPhase('post-test'),\n                        className: \"text-gray-500 hover:text-gray-700\",\n                        children: \"Lanjut ke wawancara\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                        lineNumber: 494,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                lineNumber: 459,\n                columnNumber: 9\n            }, this);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-40 h-40 mx-auto rounded-full bg-gradient-to-br \".concat(getBreathingColor(), \" flex items-center justify-center transition-all duration-1000 shadow-2xl \").concat(breathingPhase === 'inhale' ? 'scale-110' : breathingPhase === 'exhale' ? 'scale-90' : 'scale-100'),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-white text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-4xl font-bold\",\n                                children: getCurrentPhaseProgress()\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                lineNumber: 510,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm opacity-80\",\n                                children: [\n                                    getPhaseSeconds(),\n                                    \"s\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                lineNumber: 511,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-2xl mt-1\",\n                                children: getPhaseIcon()\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                lineNumber: 512,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                        lineNumber: 509,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                    lineNumber: 508,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-2xl font-medium text-gray-800\",\n                            children: getBreathingInstruction()\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                            lineNumber: 518,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-500\",\n                            children: \"Ikuti ritme pernapasan dengan tenang\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                            lineNumber: 519,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                    lineNumber: 517,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BreathingCards, {}, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                    lineNumber: 523,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gray-50 p-4 rounded-xl\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm text-gray-600 mb-2\",\n                            children: [\n                                \"Siklus ke-\",\n                                cycleCount + 1,\n                                \" dari \",\n                                TOTAL_CYCLES,\n                                \" | \",\n                                getCurrentPhaseProgress(),\n                                \"/\",\n                                getPhaseSeconds(),\n                                \" detik\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                            lineNumber: 527,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_progress__WEBPACK_IMPORTED_MODULE_4__.Progress, {\n                            value: (cycleCount * CYCLE_DURATION + count) / (TOTAL_CYCLES * CYCLE_DURATION) * 100,\n                            className: \"w-full\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                            lineNumber: 530,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-gray-500 mt-2\",\n                            children: isDone ? 'Latihan pernapasan selesai!' : \"Sisa waktu: \".concat(TOTAL_CYCLES * CYCLE_DURATION - (cycleCount * CYCLE_DURATION + count), \" detik\")\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                            lineNumber: 531,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                    lineNumber: 526,\n                    columnNumber: 9\n                }, this),\n                isDone && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4 bg-green-50 border border-green-200 rounded-xl\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-green-800 font-medium\",\n                                    children: \"✓ Latihan pernapasan selesai!\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                    lineNumber: 539,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-green-600 text-sm\",\n                                    children: [\n                                        \"Anda telah menyelesaikan \",\n                                        TOTAL_CYCLES,\n                                        \" siklus pernapasan\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                    lineNumber: 540,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-green-500 text-xs mt-1\",\n                                    children: \"Merasa lebih tenang? Anda bisa mengulang lagi atau melanjutkan.\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                    lineNumber: 541,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                            lineNumber: 538,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-4 justify-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    onClick: resetBreathingExercise,\n                                    variant: \"outline\",\n                                    className: \"border-blue-500 text-blue-600 hover:bg-blue-50 px-6 py-3 rounded-xl\",\n                                    children: \"\\uD83D\\uDD04 Ulang Lagi\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                    lineNumber: 545,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    onClick: ()=>setCurrentPhase('post-test'),\n                                    className: \"bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600 px-8 py-3 rounded-xl\",\n                                    children: \"➡️ Lanjut\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                    lineNumber: 553,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                            lineNumber: 544,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                    lineNumber: 537,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n            lineNumber: 506,\n            columnNumber: 7\n        }, this);\n    };\n    _s1(BreathingBreak, \"T2yvTh9DD4HWYTE2zl5mQ9bRNc8=\");\n    // Affirmation break for encouragement\n    const AffirmationBreak = ()=>{\n        _s2();\n        const affirmations = [\n            \"Anda sedang melakukan dengan sangat baik\",\n            \"Setiap jawaban Anda menunjukkan kemampuan yang luar biasa\",\n            \"Pengalaman Anda sangat berharga dan unik\",\n            \"Anda memiliki potensi yang besar\",\n            \"Kepercayaan diri Anda terus berkembang\",\n            \"Anda adalah kandidat yang berkualitas\"\n        ];\n        const [currentAffirmation, setCurrentAffirmation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n        const [isDone, setIsDone] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n        (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n            \"PsychologicalSafety.AffirmationBreak.useEffect\": ()=>{\n                if (!isBreakActive) return;\n                const interval = setInterval({\n                    \"PsychologicalSafety.AffirmationBreak.useEffect.interval\": ()=>{\n                        setBreakTimer({\n                            \"PsychologicalSafety.AffirmationBreak.useEffect.interval\": (prev)=>{\n                                const newTimer = prev + 1;\n                                if (newTimer % 5 === 0 && newTimer < 30) {\n                                    setCurrentAffirmation(Math.floor(newTimer / 5) % affirmations.length);\n                                }\n                                if (newTimer >= 30) {\n                                    setIsBreakActive(false);\n                                    setIsDone(true);\n                                    setIsBreakCompleted(true);\n                                }\n                                return newTimer;\n                            }\n                        }[\"PsychologicalSafety.AffirmationBreak.useEffect.interval\"]);\n                    }\n                }[\"PsychologicalSafety.AffirmationBreak.useEffect.interval\"], 1000);\n                return ({\n                    \"PsychologicalSafety.AffirmationBreak.useEffect\": ()=>clearInterval(interval)\n                })[\"PsychologicalSafety.AffirmationBreak.useEffect\"];\n            }\n        }[\"PsychologicalSafety.AffirmationBreak.useEffect\"], [\n            isBreakActive\n        ]);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-32 h-32 mx-auto rounded-full bg-gradient-to-br from-green-400 to-emerald-600 flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Coffee_Heart_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        className: \"h-12 w-12 text-white\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                        lineNumber: 604,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                    lineNumber: 603,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-xl font-semibold text-gray-800\",\n                            children: \"Pesan Positif untuk Anda\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                            lineNumber: 607,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-lg text-gray-700 font-medium bg-green-50 p-4 rounded-lg\",\n                            children: affirmations[currentAffirmation]\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                            lineNumber: 608,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-gray-500\",\n                            children: isDone ? 'Sesi afirmasi selesai!' : \"Sisa waktu: \".concat(30 - breakTimer, \" detik\")\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                            lineNumber: 611,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                    lineNumber: 606,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_progress__WEBPACK_IMPORTED_MODULE_4__.Progress, {\n                    value: breakTimer / 30 * 100,\n                    className: \"w-64 mx-auto\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                    lineNumber: 615,\n                    columnNumber: 9\n                }, this),\n                isDone && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                    onClick: onBreakComplete,\n                    className: \"bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600\",\n                    children: \"Selesai - Lanjutkan Interview\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                    lineNumber: 618,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n            lineNumber: 602,\n            columnNumber: 7\n        }, this);\n    };\n    _s2(AffirmationBreak, \"1IfL/HKV9L5/h2C8yQtjNxZXuFs=\");\n    // Rest break for general relaxation\n    const RestBreak = ()=>{\n        _s3();\n        const [isDone, setIsDone] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n        (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n            \"PsychologicalSafety.RestBreak.useEffect\": ()=>{\n                if (!isBreakActive) return;\n                const interval = setInterval({\n                    \"PsychologicalSafety.RestBreak.useEffect.interval\": ()=>{\n                        setBreakTimer({\n                            \"PsychologicalSafety.RestBreak.useEffect.interval\": (prev)=>{\n                                const newTimer = prev + 1;\n                                if (newTimer >= 20) {\n                                    setIsBreakActive(false);\n                                    setIsDone(true);\n                                    setIsBreakCompleted(true);\n                                }\n                                return newTimer;\n                            }\n                        }[\"PsychologicalSafety.RestBreak.useEffect.interval\"]);\n                    }\n                }[\"PsychologicalSafety.RestBreak.useEffect.interval\"], 1000);\n                return ({\n                    \"PsychologicalSafety.RestBreak.useEffect\": ()=>clearInterval(interval)\n                })[\"PsychologicalSafety.RestBreak.useEffect\"];\n            }\n        }[\"PsychologicalSafety.RestBreak.useEffect\"], [\n            isBreakActive\n        ]);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-32 h-32 mx-auto rounded-full bg-gradient-to-br from-purple-400 to-indigo-600 flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Coffee_Heart_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        className: \"h-12 w-12 text-white\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                        lineNumber: 654,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                    lineNumber: 653,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-xl font-semibold text-gray-800\",\n                            children: \"Waktu Istirahat Sejenak\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                            lineNumber: 657,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 max-w-md mx-auto\",\n                            children: \"Ambil waktu sejenak untuk rileks. Anda sudah melakukan dengan baik sejauh ini.\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                            lineNumber: 658,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-gray-500\",\n                            children: isDone ? 'Waktu istirahat selesai!' : \"Sisa waktu: \".concat(20 - breakTimer, \" detik\")\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                            lineNumber: 661,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                    lineNumber: 656,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_progress__WEBPACK_IMPORTED_MODULE_4__.Progress, {\n                    value: breakTimer / 20 * 100,\n                    className: \"w-64 mx-auto\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                    lineNumber: 665,\n                    columnNumber: 9\n                }, this),\n                isDone && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                    onClick: onBreakComplete,\n                    className: \"bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600\",\n                    children: \"Selesai - Lanjutkan Interview\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                    lineNumber: 668,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n            lineNumber: 652,\n            columnNumber: 7\n        }, this);\n    };\n    _s3(RestBreak, \"5o5A2HD/StWRXO0HglpKzpDn2bM=\");\n    if (!isVisible) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n            className: \"w-full max-w-2xl\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                    className: \"bg-gradient-to-r from-blue-500 to-purple-500 text-white text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                            className: \"text-2xl font-bold\",\n                            children: \"Psychological Safety Break\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                            lineNumber: 685,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-blue-100\",\n                            children: \"Mari ambil waktu sejenak untuk diri Anda\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                            lineNumber: 686,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                    lineNumber: 684,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                    className: \"p-8\",\n                    children: [\n                        currentPhase === 'pre-test' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PreTestComponent, {}, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                            lineNumber: 689,\n                            columnNumber: 43\n                        }, this),\n                        currentPhase === 'instructions' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(InstructionsComponent, {}, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                            lineNumber: 690,\n                            columnNumber: 47\n                        }, this),\n                        currentPhase === 'breathing' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                breakType === 'breathing' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BreathingBreak, {}, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                    lineNumber: 693,\n                                    columnNumber: 45\n                                }, this),\n                                breakType === 'affirmation' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AffirmationBreak, {}, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                    lineNumber: 694,\n                                    columnNumber: 47\n                                }, this),\n                                breakType === 'rest' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(RestBreak, {}, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                    lineNumber: 695,\n                                    columnNumber: 40\n                                }, this)\n                            ]\n                        }, void 0, true),\n                        currentPhase === 'post-test' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PostTestComponent, {}, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                            lineNumber: 698,\n                            columnNumber: 44\n                        }, this),\n                        currentPhase === 'completed' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CompletedComponent, {}, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                            lineNumber: 699,\n                            columnNumber: 44\n                        }, this),\n                        currentPhase === 'breathing' && !isBreakCompleted && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-8 text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"outline\",\n                                    onClick: onClose,\n                                    className: \"mr-4\",\n                                    children: \"Lewati Break\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                    lineNumber: 703,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    onClick: ()=>{\n                                        setIsBreakActive(false);\n                                        setIsBreakCompleted(true);\n                                    },\n                                    disabled: isBreakActive,\n                                    className: \"bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600\",\n                                    children: isBreakActive ? 'Sedang Break...' : 'Selesaikan Break'\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                    lineNumber: 710,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                            lineNumber: 702,\n                            columnNumber: 13\n                        }, this),\n                        currentPhase === 'breathing' && isBreakCompleted && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-8 text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-4 p-3 bg-green-50 border border-green-200 rounded-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-green-800 font-medium\",\n                                        children: \"✓ Break selesai! Lanjutkan ke evaluasi.\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                        lineNumber: 726,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                    lineNumber: 725,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    onClick: ()=>setCurrentPhase('post-test'),\n                                    className: \"bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600 text-lg px-8 py-3\",\n                                    children: \"Lanjutkan ke Evaluasi\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                    lineNumber: 728,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                            lineNumber: 724,\n                            columnNumber: 13\n                        }, this),\n                        (currentPhase === 'pre-test' || currentPhase === 'instructions') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-8 text-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"outline\",\n                                onClick: onClose,\n                                children: \"Lewati Semua\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                lineNumber: 739,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                            lineNumber: 738,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                    lineNumber: 688,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n            lineNumber: 683,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n        lineNumber: 682,\n        columnNumber: 5\n    }, this);\n}\n_s(PsychologicalSafety, \"T80EamEx/gj3kiCRem+DYCNDD4Q=\");\n_c = PsychologicalSafety;\nvar _c;\n$RefreshReg$(_c, \"PsychologicalSafety\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/psychological-safety.tsx\n"));

/***/ })

});