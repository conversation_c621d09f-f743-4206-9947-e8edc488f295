"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./components/psychological-safety.tsx":
/*!*********************************************!*\
  !*** ./components/psychological-safety.tsx ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PsychologicalSafety)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_progress__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/progress */ \"(app-pages-browser)/./components/ui/progress.tsx\");\n/* harmony import */ var _barrel_optimize_names_Coffee_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Coffee,Shield!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Coffee_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Coffee,Shield!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/coffee.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction PsychologicalSafety(param) {\n    let { isVisible, onClose, onBreakComplete, profile, stressLevel = 'medium' } = param;\n    _s();\n    var _s1 = $RefreshSig$(), _s2 = $RefreshSig$(), _s3 = $RefreshSig$();\n    const [breakType, setBreakType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('breathing');\n    const [breakTimer, setBreakTimer] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [isBreakActive, setIsBreakActive] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isBreakCompleted, setIsBreakCompleted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [breathingPhase, setBreathingPhase] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('inhale');\n    // Pre-post test states\n    const [currentPhase, setCurrentPhase] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('pre-test');\n    const [preTestData, setPreTestData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        feeling: '',\n        score: 5\n    });\n    const [postTestData, setPostTestData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        feeling: '',\n        score: 5\n    });\n    // Auto-start break when component becomes visible\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PsychologicalSafety.useEffect\": ()=>{\n            if (isVisible && currentPhase === 'breathing' && !isBreakActive) {\n                startBreak();\n            }\n        }\n    }[\"PsychologicalSafety.useEffect\"], [\n        isVisible,\n        currentPhase\n    ]);\n    const startBreak = ()=>{\n        setIsBreakActive(true);\n        setBreakTimer(0);\n        // Choose break type based on stress level and profile\n        if (stressLevel === 'high' || profile && profile.anxietyLevel === 'high') {\n            setBreakType('breathing');\n        } else if (profile && profile.needsEncouragement) {\n            setBreakType('affirmation');\n        } else {\n            setBreakType('rest');\n        }\n    };\n    // Pre-test component\n    const PreTestComponent = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-32 h-32 mx-auto rounded-full bg-gradient-to-br from-blue-400 to-blue-600 flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-white text-4xl\",\n                        children: \"\\uD83D\\uDCDD\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                    lineNumber: 67,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-2xl font-semibold text-gray-800\",\n                            children: \"Sebelum Latihan Pernapasan\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                            lineNumber: 71,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: \"Mari kita ukur perasaan Anda saat ini\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                            lineNumber: 72,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-50 p-6 rounded-lg space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Bagaimana perasaan Anda saat ini?\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                            lineNumber: 76,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                            value: preTestData.feeling,\n                                            onChange: (e)=>setPreTestData((prev)=>({\n                                                        ...prev,\n                                                        feeling: e.target.value\n                                                    })),\n                                            placeholder: \"Contoh: Saya merasa cemas dan gugup...\",\n                                            className: \"w-full p-3 border border-gray-300 rounded-lg resize-none h-20\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                            lineNumber: 79,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                    lineNumber: 75,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Seberapa tenang perasaan Anda? (1 = Sangat Cemas, 10 = Sangat Tenang)\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                            lineNumber: 88,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-gray-500\",\n                                                    children: \"1\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                                    lineNumber: 92,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"range\",\n                                                    min: \"1\",\n                                                    max: \"10\",\n                                                    value: preTestData.score,\n                                                    onChange: (e)=>setPreTestData((prev)=>({\n                                                                ...prev,\n                                                                score: parseInt(e.target.value)\n                                                            })),\n                                                    className: \"flex-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                                    lineNumber: 93,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-gray-500\",\n                                                    children: \"10\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                                    lineNumber: 101,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                            lineNumber: 91,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center mt-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-2xl font-bold text-blue-600\",\n                                                children: preTestData.score\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                                lineNumber: 104,\n                                                columnNumber: 15\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                            lineNumber: 103,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                    lineNumber: 87,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                            lineNumber: 74,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            onClick: ()=>setCurrentPhase('instructions'),\n                            disabled: !preTestData.feeling.trim(),\n                            className: \"bg-gradient-to-r from-blue-500 to-indigo-500 hover:from-blue-600 hover:to-indigo-600\",\n                            children: \"Lanjutkan ke Instruksi\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                            lineNumber: 109,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                    lineNumber: 70,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n            lineNumber: 66,\n            columnNumber: 5\n        }, this);\n    // Instructions component with visual grid\n    const InstructionsComponent = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-32 h-32 mx-auto rounded-full bg-gradient-to-br from-green-400 to-emerald-600 flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-white text-4xl\",\n                        children: \"\\uD83D\\uDCCB\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                        lineNumber: 124,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                    lineNumber: 123,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-2xl font-semibold text-gray-800\",\n                            children: \"Instruksi Latihan Pernapasan\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                            lineNumber: 127,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: \"Ikuti pola pernapasan berikut untuk merasa lebih tenang\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                            lineNumber: 128,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-50 p-6 rounded-lg space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-600 mb-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Pola Pernapasan 4-3-4:\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                        lineNumber: 132,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                    lineNumber: 131,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-4 gap-2 w-48 mx-auto mb-4\",\n                                    children: Array.from({\n                                        length: 12\n                                    }, (_, i)=>{\n                                        let squareColor = 'bg-gray-200';\n                                        let label = '';\n                                        if (i < 4) {\n                                            squareColor = 'bg-blue-500';\n                                            label = 'T';\n                                        } else if (i < 7) {\n                                            squareColor = 'bg-yellow-500';\n                                            label = 'H';\n                                        } else if (i < 11) {\n                                            squareColor = 'bg-green-500';\n                                            label = 'B';\n                                        } else {\n                                            squareColor = 'bg-gray-300';\n                                            label = '↻';\n                                        }\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-10 h-10 \".concat(squareColor, \" rounded flex items-center justify-center text-white font-bold text-sm\"),\n                                            children: label\n                                        }, i, false, {\n                                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                            lineNumber: 156,\n                                            columnNumber: 17\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                    lineNumber: 136,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2 text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-4 h-4 bg-blue-500 rounded\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                                    lineNumber: 168,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"T (Tarik):\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                                            lineNumber: 169,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \" Tarik napas dalam-dalam selama 4 detik\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                                    lineNumber: 169,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                            lineNumber: 167,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-4 h-4 bg-yellow-500 rounded\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                                    lineNumber: 172,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"H (Tahan):\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                                            lineNumber: 173,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \" Tahan napas selama 3 detik\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                                    lineNumber: 173,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                            lineNumber: 171,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-4 h-4 bg-green-500 rounded\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                                    lineNumber: 176,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"B (Buang):\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                                            lineNumber: 177,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \" Hembuskan napas perlahan selama 4 detik\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                                    lineNumber: 177,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                            lineNumber: 175,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                    lineNumber: 166,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                            lineNumber: 130,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            onClick: ()=>setCurrentPhase('breathing'),\n                            className: \"bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600\",\n                            children: \"Mulai Latihan Pernapasan\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                            lineNumber: 182,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                    lineNumber: 126,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n            lineNumber: 122,\n            columnNumber: 5\n        }, this);\n    // Breathing exercise for high stress\n    const BreathingBreak = ()=>{\n        _s1();\n        const [count, setCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n        const [cycleCount, setCycleCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n        const [isDone, setIsDone] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n        (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n            \"PsychologicalSafety.BreathingBreak.useEffect\": ()=>{\n                if (!isBreakActive) return;\n                const interval = setInterval({\n                    \"PsychologicalSafety.BreathingBreak.useEffect.interval\": ()=>{\n                        setCount({\n                            \"PsychologicalSafety.BreathingBreak.useEffect.interval\": (prev)=>{\n                                const newCount = prev + 1;\n                                setBreakTimer(newCount);\n                                if (newCount <= 4) {\n                                    setBreathingPhase('inhale');\n                                } else if (newCount <= 7) {\n                                    setBreathingPhase('hold');\n                                } else if (newCount <= 11) {\n                                    setBreathingPhase('exhale');\n                                } else {\n                                    setCount(0);\n                                    setCycleCount({\n                                        \"PsychologicalSafety.BreathingBreak.useEffect.interval\": (prevCycle)=>prevCycle + 1\n                                    }[\"PsychologicalSafety.BreathingBreak.useEffect.interval\"]);\n                                    return 0;\n                                }\n                                if (newCount >= 60) {\n                                    setIsBreakActive(false);\n                                    setIsDone(true);\n                                    setIsBreakCompleted(true);\n                                }\n                                return newCount;\n                            }\n                        }[\"PsychologicalSafety.BreathingBreak.useEffect.interval\"]);\n                    }\n                }[\"PsychologicalSafety.BreathingBreak.useEffect.interval\"], 1000);\n                return ({\n                    \"PsychologicalSafety.BreathingBreak.useEffect\": ()=>clearInterval(interval)\n                })[\"PsychologicalSafety.BreathingBreak.useEffect\"];\n            }\n        }[\"PsychologicalSafety.BreathingBreak.useEffect\"], [\n            isBreakActive\n        ]);\n        const getBreathingInstruction = ()=>{\n            switch(breathingPhase){\n                case 'inhale':\n                    return 'Tarik napas dalam-dalam...';\n                case 'hold':\n                    return 'Tahan napas...';\n                case 'exhale':\n                    return 'Hembuskan perlahan...';\n            }\n        };\n        const getBreathingColor = ()=>{\n            switch(breathingPhase){\n                case 'inhale':\n                    return 'from-blue-400 to-blue-600';\n                case 'hold':\n                    return 'from-yellow-400 to-yellow-600';\n                case 'exhale':\n                    return 'from-green-400 to-green-600';\n            }\n        };\n        const getPhaseSeconds = ()=>{\n            switch(breathingPhase){\n                case 'inhale':\n                    return 4;\n                case 'hold':\n                    return 3;\n                case 'exhale':\n                    return 4;\n            }\n        };\n        const getCurrentPhaseProgress = ()=>{\n            const phaseStart = breathingPhase === 'inhale' ? 0 : breathingPhase === 'hold' ? 4 : 7;\n            const currentInPhase = count - phaseStart;\n            return Math.min(currentInPhase, getPhaseSeconds());\n        };\n        // Visual breathing pattern grid\n        const BreathingGrid = ()=>{\n            const totalSquares = 12 // 4 + 3 + 4 + 1 (for reset)\n            ;\n            const activeSquares = count === 0 ? 0 : count > 11 ? 11 : count;\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-4 gap-2 w-48 mx-auto mb-4\",\n                children: Array.from({\n                    length: totalSquares\n                }, (_, i)=>{\n                    let squareColor = 'bg-gray-200';\n                    let label = '';\n                    if (i < 4) {\n                        // Inhale squares\n                        squareColor = i < activeSquares ? 'bg-blue-500' : 'bg-gray-200';\n                        label = 'T';\n                    } else if (i < 7) {\n                        // Hold squares\n                        squareColor = i < activeSquares ? 'bg-yellow-500' : 'bg-gray-200';\n                        label = 'H';\n                    } else if (i < 11) {\n                        // Exhale squares\n                        squareColor = i < activeSquares ? 'bg-green-500' : 'bg-gray-200';\n                        label = 'B';\n                    } else {\n                        // Reset square\n                        squareColor = 'bg-gray-300';\n                        label = '↻';\n                    }\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-10 h-10 \".concat(squareColor, \" rounded flex items-center justify-center text-white font-bold text-sm transition-colors duration-300\"),\n                        children: label\n                    }, i, false, {\n                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                        lineNumber: 291,\n                        columnNumber: 15\n                    }, this);\n                })\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                lineNumber: 267,\n                columnNumber: 9\n            }, this);\n        };\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-32 h-32 mx-auto rounded-full bg-gradient-to-br \".concat(getBreathingColor(), \" flex items-center justify-center transition-all duration-1000 \").concat(breathingPhase === 'inhale' ? 'scale-110' : breathingPhase === 'exhale' ? 'scale-90' : 'scale-100'),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-white text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-3xl font-bold\",\n                                children: getCurrentPhaseProgress()\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                lineNumber: 307,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs\",\n                                children: [\n                                    getPhaseSeconds(),\n                                    \"s\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                lineNumber: 308,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                        lineNumber: 306,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                    lineNumber: 305,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl font-medium text-gray-800\",\n                            children: getBreathingInstruction()\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                            lineNumber: 313,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-50 p-4 rounded-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-600 mb-2\",\n                                    children: [\n                                        \"Pola Pernapasan: \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-blue-600\",\n                                            children: \"Tarik (T)\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                            lineNumber: 318,\n                                            columnNumber: 32\n                                        }, this),\n                                        \" → \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-yellow-600\",\n                                            children: \"Tahan (H)\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                            lineNumber: 318,\n                                            columnNumber: 83\n                                        }, this),\n                                        \" → \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-green-600\",\n                                            children: \"Buang (B)\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                            lineNumber: 318,\n                                            columnNumber: 136\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                    lineNumber: 317,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BreathingGrid, {}, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                    lineNumber: 320,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-gray-500\",\n                                    children: [\n                                        \"Siklus ke-\",\n                                        cycleCount + 1,\n                                        \" | \",\n                                        getCurrentPhaseProgress(),\n                                        \"/\",\n                                        getPhaseSeconds(),\n                                        \" detik\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                    lineNumber: 321,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                            lineNumber: 316,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-gray-500\",\n                            children: isDone ? 'Latihan pernapasan selesai!' : \"Sisa waktu: \".concat(60 - breakTimer, \" detik\")\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                            lineNumber: 326,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                    lineNumber: 312,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_progress__WEBPACK_IMPORTED_MODULE_4__.Progress, {\n                    value: breakTimer / 60 * 100,\n                    className: \"w-64 mx-auto\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                    lineNumber: 331,\n                    columnNumber: 9\n                }, this),\n                isDone && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                    onClick: onBreakComplete,\n                    className: \"bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600\",\n                    children: \"Selesai - Lanjutkan Interview\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                    lineNumber: 334,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n            lineNumber: 304,\n            columnNumber: 7\n        }, this);\n    };\n    _s1(BreathingBreak, \"ib2+92dWM39O1g9jwudgI1JaKG0=\");\n    // Affirmation break for encouragement\n    const AffirmationBreak = ()=>{\n        _s2();\n        const affirmations = [\n            \"Anda sedang melakukan dengan sangat baik\",\n            \"Setiap jawaban Anda menunjukkan kemampuan yang luar biasa\",\n            \"Pengalaman Anda sangat berharga dan unik\",\n            \"Anda memiliki potensi yang besar\",\n            \"Kepercayaan diri Anda terus berkembang\",\n            \"Anda adalah kandidat yang berkualitas\"\n        ];\n        const [currentAffirmation, setCurrentAffirmation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n        const [isDone, setIsDone] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n        (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n            \"PsychologicalSafety.AffirmationBreak.useEffect\": ()=>{\n                if (!isBreakActive) return;\n                const interval = setInterval({\n                    \"PsychologicalSafety.AffirmationBreak.useEffect.interval\": ()=>{\n                        setBreakTimer({\n                            \"PsychologicalSafety.AffirmationBreak.useEffect.interval\": (prev)=>{\n                                const newTimer = prev + 1;\n                                if (newTimer % 5 === 0 && newTimer < 30) {\n                                    setCurrentAffirmation(Math.floor(newTimer / 5) % affirmations.length);\n                                }\n                                if (newTimer >= 30) {\n                                    setIsBreakActive(false);\n                                    setIsDone(true);\n                                    setIsBreakCompleted(true);\n                                }\n                                return newTimer;\n                            }\n                        }[\"PsychologicalSafety.AffirmationBreak.useEffect.interval\"]);\n                    }\n                }[\"PsychologicalSafety.AffirmationBreak.useEffect.interval\"], 1000);\n                return ({\n                    \"PsychologicalSafety.AffirmationBreak.useEffect\": ()=>clearInterval(interval)\n                })[\"PsychologicalSafety.AffirmationBreak.useEffect\"];\n            }\n        }[\"PsychologicalSafety.AffirmationBreak.useEffect\"], [\n            isBreakActive\n        ]);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-32 h-32 mx-auto rounded-full bg-gradient-to-br from-green-400 to-emerald-600 flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Coffee_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        className: \"h-12 w-12 text-white\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                        lineNumber: 383,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                    lineNumber: 382,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-xl font-semibold text-gray-800\",\n                            children: \"Pesan Positif untuk Anda\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                            lineNumber: 386,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-lg text-gray-700 font-medium bg-green-50 p-4 rounded-lg\",\n                            children: affirmations[currentAffirmation]\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                            lineNumber: 387,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-gray-500\",\n                            children: isDone ? 'Sesi afirmasi selesai!' : \"Sisa waktu: \".concat(30 - breakTimer, \" detik\")\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                            lineNumber: 390,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                    lineNumber: 385,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_progress__WEBPACK_IMPORTED_MODULE_4__.Progress, {\n                    value: breakTimer / 30 * 100,\n                    className: \"w-64 mx-auto\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                    lineNumber: 394,\n                    columnNumber: 9\n                }, this),\n                isDone && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                    onClick: onBreakComplete,\n                    className: \"bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600\",\n                    children: \"Selesai - Lanjutkan Interview\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                    lineNumber: 397,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n            lineNumber: 381,\n            columnNumber: 7\n        }, this);\n    };\n    _s2(AffirmationBreak, \"1IfL/HKV9L5/h2C8yQtjNxZXuFs=\");\n    // Rest break for general relaxation\n    const RestBreak = ()=>{\n        _s3();\n        const [isDone, setIsDone] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n        (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n            \"PsychologicalSafety.RestBreak.useEffect\": ()=>{\n                if (!isBreakActive) return;\n                const interval = setInterval({\n                    \"PsychologicalSafety.RestBreak.useEffect.interval\": ()=>{\n                        setBreakTimer({\n                            \"PsychologicalSafety.RestBreak.useEffect.interval\": (prev)=>{\n                                const newTimer = prev + 1;\n                                if (newTimer >= 20) {\n                                    setIsBreakActive(false);\n                                    setIsDone(true);\n                                    setIsBreakCompleted(true);\n                                }\n                                return newTimer;\n                            }\n                        }[\"PsychologicalSafety.RestBreak.useEffect.interval\"]);\n                    }\n                }[\"PsychologicalSafety.RestBreak.useEffect.interval\"], 1000);\n                return ({\n                    \"PsychologicalSafety.RestBreak.useEffect\": ()=>clearInterval(interval)\n                })[\"PsychologicalSafety.RestBreak.useEffect\"];\n            }\n        }[\"PsychologicalSafety.RestBreak.useEffect\"], [\n            isBreakActive\n        ]);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-32 h-32 mx-auto rounded-full bg-gradient-to-br from-purple-400 to-indigo-600 flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Coffee_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        className: \"h-12 w-12 text-white\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                        lineNumber: 433,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                    lineNumber: 432,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-xl font-semibold text-gray-800\",\n                            children: \"Waktu Istirahat Sejenak\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                            lineNumber: 436,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 max-w-md mx-auto\",\n                            children: \"Ambil waktu sejenak untuk rileks. Anda sudah melakukan dengan baik sejauh ini.\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                            lineNumber: 437,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-gray-500\",\n                            children: isDone ? 'Waktu istirahat selesai!' : \"Sisa waktu: \".concat(20 - breakTimer, \" detik\")\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                            lineNumber: 440,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                    lineNumber: 435,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_progress__WEBPACK_IMPORTED_MODULE_4__.Progress, {\n                    value: breakTimer / 20 * 100,\n                    className: \"w-64 mx-auto\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                    lineNumber: 444,\n                    columnNumber: 9\n                }, this),\n                isDone && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                    onClick: onBreakComplete,\n                    className: \"bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600\",\n                    children: \"Selesai - Lanjutkan Interview\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                    lineNumber: 447,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n            lineNumber: 431,\n            columnNumber: 7\n        }, this);\n    };\n    _s3(RestBreak, \"5o5A2HD/StWRXO0HglpKzpDn2bM=\");\n    if (!isVisible) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n            className: \"w-full max-w-2xl\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                    className: \"bg-gradient-to-r from-blue-500 to-purple-500 text-white text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                            className: \"text-2xl font-bold\",\n                            children: \"Psychological Safety Break\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                            lineNumber: 464,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-blue-100\",\n                            children: \"Mari ambil waktu sejenak untuk diri Anda\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                            lineNumber: 465,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                    lineNumber: 463,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                    className: \"p-8\",\n                    children: [\n                        breakType === 'breathing' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BreathingBreak, {}, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                            lineNumber: 468,\n                            columnNumber: 41\n                        }, this),\n                        breakType === 'affirmation' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AffirmationBreak, {}, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                            lineNumber: 469,\n                            columnNumber: 43\n                        }, this),\n                        breakType === 'rest' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(RestBreak, {}, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                            lineNumber: 470,\n                            columnNumber: 36\n                        }, this),\n                        !isBreakCompleted && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-8 text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"outline\",\n                                    onClick: onClose,\n                                    className: \"mr-4\",\n                                    children: \"Lewati Break\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                    lineNumber: 474,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    onClick: ()=>{\n                                        setIsBreakActive(false);\n                                        setIsBreakCompleted(true);\n                                    },\n                                    disabled: isBreakActive,\n                                    className: \"bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600\",\n                                    children: isBreakActive ? 'Sedang Break...' : 'Selesaikan Break'\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                    lineNumber: 481,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                            lineNumber: 473,\n                            columnNumber: 13\n                        }, this),\n                        isBreakCompleted && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-8 text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-4 p-3 bg-green-50 border border-green-200 rounded-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-green-800 font-medium\",\n                                        children: \"✓ Break selesai! Anda siap melanjutkan interview.\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                        lineNumber: 497,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                    lineNumber: 496,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    onClick: onBreakComplete,\n                                    className: \"bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600 text-lg px-8 py-3\",\n                                    children: \"Lanjutkan Interview\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                    lineNumber: 499,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                            lineNumber: 495,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                    lineNumber: 467,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n            lineNumber: 462,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n        lineNumber: 461,\n        columnNumber: 5\n    }, this);\n}\n_s(PsychologicalSafety, \"T80EamEx/gj3kiCRem+DYCNDD4Q=\");\n_c = PsychologicalSafety;\nvar _c;\n$RefreshReg$(_c, \"PsychologicalSafety\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/psychological-safety.tsx\n"));

/***/ })

});