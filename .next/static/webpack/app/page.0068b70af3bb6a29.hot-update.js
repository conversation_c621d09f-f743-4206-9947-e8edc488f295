"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./components/psychological-safety.tsx":
/*!*********************************************!*\
  !*** ./components/psychological-safety.tsx ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PsychologicalSafety)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_@opentelemetry+api@1.9.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_progress__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/progress */ \"(app-pages-browser)/./components/ui/progress.tsx\");\n/* harmony import */ var _barrel_optimize_names_Coffee_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Coffee,Shield!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Coffee_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Coffee,Shield!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/coffee.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction PsychologicalSafety(param) {\n    let { isVisible, onClose, onBreakComplete, profile, stressLevel = 'medium' } = param;\n    _s();\n    var _s1 = $RefreshSig$(), _s2 = $RefreshSig$(), _s3 = $RefreshSig$();\n    const [breakType, setBreakType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('breathing');\n    const [breakTimer, setBreakTimer] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [isBreakActive, setIsBreakActive] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isBreakCompleted, setIsBreakCompleted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [breathingPhase, setBreathingPhase] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('inhale');\n    // Pre-post test states\n    const [currentPhase, setCurrentPhase] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('pre-test');\n    const [preTestData, setPreTestData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        feeling: '',\n        score: 5\n    });\n    const [postTestData, setPostTestData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        feeling: '',\n        score: 5\n    });\n    // Auto-start break when component becomes visible\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PsychologicalSafety.useEffect\": ()=>{\n            if (isVisible && currentPhase === 'breathing' && !isBreakActive) {\n                startBreak();\n            }\n        }\n    }[\"PsychologicalSafety.useEffect\"], [\n        isVisible,\n        currentPhase\n    ]);\n    const startBreak = ()=>{\n        setIsBreakActive(true);\n        setBreakTimer(0);\n        // Choose break type based on stress level and profile\n        if (stressLevel === 'high' || profile && profile.anxietyLevel === 'high') {\n            setBreakType('breathing');\n        } else if (profile && profile.needsEncouragement) {\n            setBreakType('affirmation');\n        } else {\n            setBreakType('rest');\n        }\n    };\n    // Pre-test component\n    const PreTestComponent = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-32 h-32 mx-auto rounded-full bg-gradient-to-br from-blue-400 to-blue-600 flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-white text-4xl\",\n                        children: \"\\uD83D\\uDCDD\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                    lineNumber: 67,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-2xl font-semibold text-gray-800\",\n                            children: \"Sebelum Latihan Pernapasan\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                            lineNumber: 71,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: \"Mari kita ukur perasaan Anda saat ini\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                            lineNumber: 72,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-50 p-6 rounded-lg space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Bagaimana perasaan Anda saat ini?\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                            lineNumber: 76,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                            value: preTestData.feeling,\n                                            onChange: (e)=>setPreTestData((prev)=>({\n                                                        ...prev,\n                                                        feeling: e.target.value\n                                                    })),\n                                            placeholder: \"Contoh: Saya merasa cemas dan gugup...\",\n                                            className: \"w-full p-3 border border-gray-300 rounded-lg resize-none h-20\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                            lineNumber: 79,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                    lineNumber: 75,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Seberapa tenang perasaan Anda? (1 = Sangat Cemas, 10 = Sangat Tenang)\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                            lineNumber: 88,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-gray-500\",\n                                                    children: \"1\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                                    lineNumber: 92,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"range\",\n                                                    min: \"1\",\n                                                    max: \"10\",\n                                                    value: preTestData.score,\n                                                    onChange: (e)=>setPreTestData((prev)=>({\n                                                                ...prev,\n                                                                score: parseInt(e.target.value)\n                                                            })),\n                                                    className: \"flex-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                                    lineNumber: 93,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-gray-500\",\n                                                    children: \"10\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                                    lineNumber: 101,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                            lineNumber: 91,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center mt-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-2xl font-bold text-blue-600\",\n                                                children: preTestData.score\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                                lineNumber: 104,\n                                                columnNumber: 15\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                            lineNumber: 103,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                    lineNumber: 87,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                            lineNumber: 74,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            onClick: ()=>setCurrentPhase('instructions'),\n                            disabled: !preTestData.feeling.trim(),\n                            className: \"bg-gradient-to-r from-blue-500 to-indigo-500 hover:from-blue-600 hover:to-indigo-600\",\n                            children: \"Lanjutkan ke Instruksi\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                            lineNumber: 109,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                    lineNumber: 70,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n            lineNumber: 66,\n            columnNumber: 5\n        }, this);\n    // Instructions component with visual grid\n    const InstructionsComponent = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-32 h-32 mx-auto rounded-full bg-gradient-to-br from-green-400 to-emerald-600 flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-white text-4xl\",\n                        children: \"\\uD83D\\uDCCB\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                        lineNumber: 124,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                    lineNumber: 123,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-2xl font-semibold text-gray-800\",\n                            children: \"Instruksi Latihan Pernapasan\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                            lineNumber: 127,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: \"Ikuti pola pernapasan berikut untuk merasa lebih tenang\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                            lineNumber: 128,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-50 p-6 rounded-lg space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-600 mb-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Pola Pernapasan 4-3-4:\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                        lineNumber: 132,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                    lineNumber: 131,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-4 gap-2 w-48 mx-auto mb-4\",\n                                    children: Array.from({\n                                        length: 12\n                                    }, (_, i)=>{\n                                        let squareColor = 'bg-gray-200';\n                                        let label = '';\n                                        if (i < 4) {\n                                            squareColor = 'bg-blue-500';\n                                            label = 'T';\n                                        } else if (i < 7) {\n                                            squareColor = 'bg-yellow-500';\n                                            label = 'H';\n                                        } else if (i < 11) {\n                                            squareColor = 'bg-green-500';\n                                            label = 'B';\n                                        } else {\n                                            squareColor = 'bg-gray-300';\n                                            label = '↻';\n                                        }\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-10 h-10 \".concat(squareColor, \" rounded flex items-center justify-center text-white font-bold text-sm\"),\n                                            children: label\n                                        }, i, false, {\n                                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                            lineNumber: 156,\n                                            columnNumber: 17\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                    lineNumber: 136,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2 text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-4 h-4 bg-blue-500 rounded\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                                    lineNumber: 168,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"T (Tarik):\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                                            lineNumber: 169,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \" Tarik napas dalam-dalam selama 4 detik\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                                    lineNumber: 169,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                            lineNumber: 167,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-4 h-4 bg-yellow-500 rounded\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                                    lineNumber: 172,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"H (Tahan):\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                                            lineNumber: 173,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \" Tahan napas selama 3 detik\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                                    lineNumber: 173,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                            lineNumber: 171,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-4 h-4 bg-green-500 rounded\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                                    lineNumber: 176,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"B (Buang):\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                                            lineNumber: 177,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \" Hembuskan napas perlahan selama 4 detik\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                                    lineNumber: 177,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                            lineNumber: 175,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                    lineNumber: 166,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                            lineNumber: 130,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            onClick: ()=>setCurrentPhase('breathing'),\n                            className: \"bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600\",\n                            children: \"Mulai Latihan Pernapasan\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                            lineNumber: 182,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                    lineNumber: 126,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n            lineNumber: 122,\n            columnNumber: 5\n        }, this);\n    // Post-test component\n    const PostTestComponent = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-32 h-32 mx-auto rounded-full bg-gradient-to-br from-purple-400 to-purple-600 flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-white text-4xl\",\n                        children: \"✅\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                        lineNumber: 196,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                    lineNumber: 195,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-2xl font-semibold text-gray-800\",\n                            children: \"Setelah Latihan Pernapasan\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                            lineNumber: 199,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: \"Bagaimana perasaan Anda sekarang?\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                            lineNumber: 200,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-50 p-6 rounded-lg space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Bagaimana perasaan Anda sekarang?\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                            lineNumber: 204,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                            value: postTestData.feeling,\n                                            onChange: (e)=>setPostTestData((prev)=>({\n                                                        ...prev,\n                                                        feeling: e.target.value\n                                                    })),\n                                            placeholder: \"Contoh: Saya merasa lebih tenang dan rileks...\",\n                                            className: \"w-full p-3 border border-gray-300 rounded-lg resize-none h-20\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                            lineNumber: 207,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                    lineNumber: 203,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Seberapa tenang perasaan Anda sekarang? (1 = Sangat Cemas, 10 = Sangat Tenang)\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                            lineNumber: 216,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-gray-500\",\n                                                    children: \"1\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                                    lineNumber: 220,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"range\",\n                                                    min: \"1\",\n                                                    max: \"10\",\n                                                    value: postTestData.score,\n                                                    onChange: (e)=>setPostTestData((prev)=>({\n                                                                ...prev,\n                                                                score: parseInt(e.target.value)\n                                                            })),\n                                                    className: \"flex-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                                    lineNumber: 221,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-gray-500\",\n                                                    children: \"10\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                                    lineNumber: 229,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                            lineNumber: 219,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center mt-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-2xl font-bold text-purple-600\",\n                                                children: postTestData.score\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                                lineNumber: 232,\n                                                columnNumber: 15\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                            lineNumber: 231,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                    lineNumber: 215,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-blue-50 p-4 rounded-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-medium text-blue-800 mb-2\",\n                                            children: \"Perbandingan:\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                            lineNumber: 238,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm space-y-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        \"Sebelum: \",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-bold\",\n                                                            children: [\n                                                                preTestData.score,\n                                                                \"/10\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                                            lineNumber: 240,\n                                                            columnNumber: 29\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                                    lineNumber: 240,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        \"Sekarang: \",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-bold\",\n                                                            children: [\n                                                                postTestData.score,\n                                                                \"/10\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                                            lineNumber: 241,\n                                                            columnNumber: 30\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                                    lineNumber: 241,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"font-bold \".concat(postTestData.score > preTestData.score ? 'text-green-600' : postTestData.score < preTestData.score ? 'text-red-600' : 'text-gray-600'),\n                                                    children: [\n                                                        \"Perubahan: \",\n                                                        postTestData.score > preTestData.score ? '+' : '',\n                                                        postTestData.score - preTestData.score\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                                    lineNumber: 242,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                            lineNumber: 239,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                    lineNumber: 237,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                            lineNumber: 202,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            onClick: ()=>setCurrentPhase('completed'),\n                            disabled: !postTestData.feeling.trim(),\n                            className: \"bg-gradient-to-r from-purple-500 to-indigo-500 hover:from-purple-600 hover:to-indigo-600\",\n                            children: \"Selesai\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                            lineNumber: 249,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                    lineNumber: 198,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n            lineNumber: 194,\n            columnNumber: 5\n        }, this);\n    // Completed component\n    const CompletedComponent = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-32 h-32 mx-auto rounded-full bg-gradient-to-br from-green-400 to-emerald-600 flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-white text-4xl\",\n                        children: \"\\uD83C\\uDF89\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                        lineNumber: 264,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                    lineNumber: 263,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-2xl font-semibold text-gray-800\",\n                            children: \"Latihan Pernapasan Selesai!\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                            lineNumber: 267,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: \"Terima kasih telah mengikuti latihan pernapasan\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                            lineNumber: 268,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gradient-to-r from-green-50 to-emerald-50 p-6 rounded-lg space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"font-semibold text-green-800\",\n                                    children: \"Ringkasan Hasil:\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                    lineNumber: 271,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-2 gap-4 text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white p-3 rounded\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-gray-600\",\n                                                    children: \"Sebelum Latihan\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                                    lineNumber: 274,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl font-bold text-blue-600\",\n                                                    children: [\n                                                        preTestData.score,\n                                                        \"/10\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                                    lineNumber: 275,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                            lineNumber: 273,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white p-3 rounded\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-gray-600\",\n                                                    children: \"Setelah Latihan\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                                    lineNumber: 278,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl font-bold text-purple-600\",\n                                                    children: [\n                                                        postTestData.score,\n                                                        \"/10\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                                    lineNumber: 279,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                            lineNumber: 277,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                    lineNumber: 272,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center p-3 rounded font-bold \".concat(postTestData.score > preTestData.score ? 'bg-green-100 text-green-800' : postTestData.score < preTestData.score ? 'bg-red-100 text-red-800' : 'bg-gray-100 text-gray-800'),\n                                    children: postTestData.score > preTestData.score ? '✅ Perasaan Anda membaik!' : postTestData.score < preTestData.score ? '⚠️ Mungkin perlu latihan lebih lanjut' : '➡️ Perasaan Anda stabil'\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                    lineNumber: 282,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                            lineNumber: 270,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            onClick: onBreakComplete,\n                            className: \"bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600 text-lg px-8 py-3\",\n                            children: \"Mulai Interview\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                            lineNumber: 287,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                    lineNumber: 266,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n            lineNumber: 262,\n            columnNumber: 5\n        }, this);\n    // Breathing exercise for high stress\n    const BreathingBreak = ()=>{\n        _s1();\n        const [count, setCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n        const [cycleCount, setCycleCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n        const [isDone, setIsDone] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n        (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n            \"PsychologicalSafety.BreathingBreak.useEffect\": ()=>{\n                if (!isBreakActive) return;\n                const interval = setInterval({\n                    \"PsychologicalSafety.BreathingBreak.useEffect.interval\": ()=>{\n                        setCount({\n                            \"PsychologicalSafety.BreathingBreak.useEffect.interval\": (prev)=>{\n                                const newCount = prev + 1;\n                                setBreakTimer(newCount);\n                                if (newCount <= 4) {\n                                    setBreathingPhase('inhale');\n                                } else if (newCount <= 7) {\n                                    setBreathingPhase('hold');\n                                } else if (newCount <= 11) {\n                                    setBreathingPhase('exhale');\n                                } else {\n                                    setCount(0);\n                                    setCycleCount({\n                                        \"PsychologicalSafety.BreathingBreak.useEffect.interval\": (prevCycle)=>prevCycle + 1\n                                    }[\"PsychologicalSafety.BreathingBreak.useEffect.interval\"]);\n                                    return 0;\n                                }\n                                if (newCount >= 60) {\n                                    setIsBreakActive(false);\n                                    setIsDone(true);\n                                    setIsBreakCompleted(true);\n                                }\n                                return newCount;\n                            }\n                        }[\"PsychologicalSafety.BreathingBreak.useEffect.interval\"]);\n                    }\n                }[\"PsychologicalSafety.BreathingBreak.useEffect.interval\"], 1000);\n                return ({\n                    \"PsychologicalSafety.BreathingBreak.useEffect\": ()=>clearInterval(interval)\n                })[\"PsychologicalSafety.BreathingBreak.useEffect\"];\n            }\n        }[\"PsychologicalSafety.BreathingBreak.useEffect\"], [\n            isBreakActive\n        ]);\n        const getBreathingInstruction = ()=>{\n            switch(breathingPhase){\n                case 'inhale':\n                    return 'Tarik napas dalam-dalam...';\n                case 'hold':\n                    return 'Tahan napas...';\n                case 'exhale':\n                    return 'Hembuskan perlahan...';\n            }\n        };\n        const getBreathingColor = ()=>{\n            switch(breathingPhase){\n                case 'inhale':\n                    return 'from-blue-400 to-blue-600';\n                case 'hold':\n                    return 'from-yellow-400 to-yellow-600';\n                case 'exhale':\n                    return 'from-green-400 to-green-600';\n            }\n        };\n        const getPhaseSeconds = ()=>{\n            switch(breathingPhase){\n                case 'inhale':\n                    return 4;\n                case 'hold':\n                    return 3;\n                case 'exhale':\n                    return 4;\n            }\n        };\n        const getCurrentPhaseProgress = ()=>{\n            const phaseStart = breathingPhase === 'inhale' ? 0 : breathingPhase === 'hold' ? 4 : 7;\n            const currentInPhase = count - phaseStart;\n            return Math.min(currentInPhase, getPhaseSeconds());\n        };\n        // Visual breathing pattern grid\n        const BreathingGrid = ()=>{\n            const totalSquares = 12 // 4 + 3 + 4 + 1 (for reset)\n            ;\n            const activeSquares = count === 0 ? 0 : count > 11 ? 11 : count;\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-4 gap-2 w-48 mx-auto mb-4\",\n                children: Array.from({\n                    length: totalSquares\n                }, (_, i)=>{\n                    let squareColor = 'bg-gray-200';\n                    let label = '';\n                    if (i < 4) {\n                        // Inhale squares\n                        squareColor = i < activeSquares ? 'bg-blue-500' : 'bg-gray-200';\n                        label = 'T';\n                    } else if (i < 7) {\n                        // Hold squares\n                        squareColor = i < activeSquares ? 'bg-yellow-500' : 'bg-gray-200';\n                        label = 'H';\n                    } else if (i < 11) {\n                        // Exhale squares\n                        squareColor = i < activeSquares ? 'bg-green-500' : 'bg-gray-200';\n                        label = 'B';\n                    } else {\n                        // Reset square\n                        squareColor = 'bg-gray-300';\n                        label = '↻';\n                    }\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-10 h-10 \".concat(squareColor, \" rounded flex items-center justify-center text-white font-bold text-sm transition-colors duration-300\"),\n                        children: label\n                    }, i, false, {\n                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                        lineNumber: 396,\n                        columnNumber: 15\n                    }, this);\n                })\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                lineNumber: 372,\n                columnNumber: 9\n            }, this);\n        };\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-32 h-32 mx-auto rounded-full bg-gradient-to-br \".concat(getBreathingColor(), \" flex items-center justify-center transition-all duration-1000 \").concat(breathingPhase === 'inhale' ? 'scale-110' : breathingPhase === 'exhale' ? 'scale-90' : 'scale-100'),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-white text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-3xl font-bold\",\n                                children: getCurrentPhaseProgress()\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                lineNumber: 412,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs\",\n                                children: [\n                                    getPhaseSeconds(),\n                                    \"s\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                lineNumber: 413,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                        lineNumber: 411,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                    lineNumber: 410,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl font-medium text-gray-800\",\n                            children: getBreathingInstruction()\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                            lineNumber: 418,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-50 p-4 rounded-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-600 mb-2\",\n                                    children: [\n                                        \"Pola Pernapasan: \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-blue-600\",\n                                            children: \"Tarik (T)\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                            lineNumber: 423,\n                                            columnNumber: 32\n                                        }, this),\n                                        \" → \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-yellow-600\",\n                                            children: \"Tahan (H)\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                            lineNumber: 423,\n                                            columnNumber: 83\n                                        }, this),\n                                        \" → \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-green-600\",\n                                            children: \"Buang (B)\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                            lineNumber: 423,\n                                            columnNumber: 136\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                    lineNumber: 422,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BreathingGrid, {}, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                    lineNumber: 425,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-gray-500\",\n                                    children: [\n                                        \"Siklus ke-\",\n                                        cycleCount + 1,\n                                        \" | \",\n                                        getCurrentPhaseProgress(),\n                                        \"/\",\n                                        getPhaseSeconds(),\n                                        \" detik\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                    lineNumber: 426,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                            lineNumber: 421,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-gray-500\",\n                            children: isDone ? 'Latihan pernapasan selesai!' : \"Sisa waktu: \".concat(60 - breakTimer, \" detik\")\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                            lineNumber: 431,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                    lineNumber: 417,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_progress__WEBPACK_IMPORTED_MODULE_4__.Progress, {\n                    value: breakTimer / 60 * 100,\n                    className: \"w-64 mx-auto\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                    lineNumber: 436,\n                    columnNumber: 9\n                }, this),\n                isDone && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                    onClick: ()=>setCurrentPhase('post-test'),\n                    className: \"bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600\",\n                    children: \"Lanjutkan ke Evaluasi\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                    lineNumber: 439,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n            lineNumber: 409,\n            columnNumber: 7\n        }, this);\n    };\n    _s1(BreathingBreak, \"ib2+92dWM39O1g9jwudgI1JaKG0=\");\n    // Affirmation break for encouragement\n    const AffirmationBreak = ()=>{\n        _s2();\n        const affirmations = [\n            \"Anda sedang melakukan dengan sangat baik\",\n            \"Setiap jawaban Anda menunjukkan kemampuan yang luar biasa\",\n            \"Pengalaman Anda sangat berharga dan unik\",\n            \"Anda memiliki potensi yang besar\",\n            \"Kepercayaan diri Anda terus berkembang\",\n            \"Anda adalah kandidat yang berkualitas\"\n        ];\n        const [currentAffirmation, setCurrentAffirmation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n        const [isDone, setIsDone] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n        (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n            \"PsychologicalSafety.AffirmationBreak.useEffect\": ()=>{\n                if (!isBreakActive) return;\n                const interval = setInterval({\n                    \"PsychologicalSafety.AffirmationBreak.useEffect.interval\": ()=>{\n                        setBreakTimer({\n                            \"PsychologicalSafety.AffirmationBreak.useEffect.interval\": (prev)=>{\n                                const newTimer = prev + 1;\n                                if (newTimer % 5 === 0 && newTimer < 30) {\n                                    setCurrentAffirmation(Math.floor(newTimer / 5) % affirmations.length);\n                                }\n                                if (newTimer >= 30) {\n                                    setIsBreakActive(false);\n                                    setIsDone(true);\n                                    setIsBreakCompleted(true);\n                                }\n                                return newTimer;\n                            }\n                        }[\"PsychologicalSafety.AffirmationBreak.useEffect.interval\"]);\n                    }\n                }[\"PsychologicalSafety.AffirmationBreak.useEffect.interval\"], 1000);\n                return ({\n                    \"PsychologicalSafety.AffirmationBreak.useEffect\": ()=>clearInterval(interval)\n                })[\"PsychologicalSafety.AffirmationBreak.useEffect\"];\n            }\n        }[\"PsychologicalSafety.AffirmationBreak.useEffect\"], [\n            isBreakActive\n        ]);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-32 h-32 mx-auto rounded-full bg-gradient-to-br from-green-400 to-emerald-600 flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Coffee_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        className: \"h-12 w-12 text-white\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                        lineNumber: 488,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                    lineNumber: 487,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-xl font-semibold text-gray-800\",\n                            children: \"Pesan Positif untuk Anda\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                            lineNumber: 491,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-lg text-gray-700 font-medium bg-green-50 p-4 rounded-lg\",\n                            children: affirmations[currentAffirmation]\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                            lineNumber: 492,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-gray-500\",\n                            children: isDone ? 'Sesi afirmasi selesai!' : \"Sisa waktu: \".concat(30 - breakTimer, \" detik\")\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                            lineNumber: 495,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                    lineNumber: 490,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_progress__WEBPACK_IMPORTED_MODULE_4__.Progress, {\n                    value: breakTimer / 30 * 100,\n                    className: \"w-64 mx-auto\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                    lineNumber: 499,\n                    columnNumber: 9\n                }, this),\n                isDone && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                    onClick: onBreakComplete,\n                    className: \"bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600\",\n                    children: \"Selesai - Lanjutkan Interview\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                    lineNumber: 502,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n            lineNumber: 486,\n            columnNumber: 7\n        }, this);\n    };\n    _s2(AffirmationBreak, \"1IfL/HKV9L5/h2C8yQtjNxZXuFs=\");\n    // Rest break for general relaxation\n    const RestBreak = ()=>{\n        _s3();\n        const [isDone, setIsDone] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n        (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n            \"PsychologicalSafety.RestBreak.useEffect\": ()=>{\n                if (!isBreakActive) return;\n                const interval = setInterval({\n                    \"PsychologicalSafety.RestBreak.useEffect.interval\": ()=>{\n                        setBreakTimer({\n                            \"PsychologicalSafety.RestBreak.useEffect.interval\": (prev)=>{\n                                const newTimer = prev + 1;\n                                if (newTimer >= 20) {\n                                    setIsBreakActive(false);\n                                    setIsDone(true);\n                                    setIsBreakCompleted(true);\n                                }\n                                return newTimer;\n                            }\n                        }[\"PsychologicalSafety.RestBreak.useEffect.interval\"]);\n                    }\n                }[\"PsychologicalSafety.RestBreak.useEffect.interval\"], 1000);\n                return ({\n                    \"PsychologicalSafety.RestBreak.useEffect\": ()=>clearInterval(interval)\n                })[\"PsychologicalSafety.RestBreak.useEffect\"];\n            }\n        }[\"PsychologicalSafety.RestBreak.useEffect\"], [\n            isBreakActive\n        ]);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-32 h-32 mx-auto rounded-full bg-gradient-to-br from-purple-400 to-indigo-600 flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Coffee_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        className: \"h-12 w-12 text-white\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                        lineNumber: 538,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                    lineNumber: 537,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-xl font-semibold text-gray-800\",\n                            children: \"Waktu Istirahat Sejenak\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                            lineNumber: 541,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 max-w-md mx-auto\",\n                            children: \"Ambil waktu sejenak untuk rileks. Anda sudah melakukan dengan baik sejauh ini.\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                            lineNumber: 542,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-gray-500\",\n                            children: isDone ? 'Waktu istirahat selesai!' : \"Sisa waktu: \".concat(20 - breakTimer, \" detik\")\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                            lineNumber: 545,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                    lineNumber: 540,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_progress__WEBPACK_IMPORTED_MODULE_4__.Progress, {\n                    value: breakTimer / 20 * 100,\n                    className: \"w-64 mx-auto\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                    lineNumber: 549,\n                    columnNumber: 9\n                }, this),\n                isDone && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                    onClick: onBreakComplete,\n                    className: \"bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600\",\n                    children: \"Selesai - Lanjutkan Interview\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                    lineNumber: 552,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n            lineNumber: 536,\n            columnNumber: 7\n        }, this);\n    };\n    _s3(RestBreak, \"5o5A2HD/StWRXO0HglpKzpDn2bM=\");\n    if (!isVisible) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n            className: \"w-full max-w-2xl\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                    className: \"bg-gradient-to-r from-blue-500 to-purple-500 text-white text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                            className: \"text-2xl font-bold\",\n                            children: \"Psychological Safety Break\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                            lineNumber: 569,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-blue-100\",\n                            children: \"Mari ambil waktu sejenak untuk diri Anda\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                            lineNumber: 570,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                    lineNumber: 568,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                    className: \"p-8\",\n                    children: [\n                        breakType === 'breathing' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BreathingBreak, {}, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                            lineNumber: 573,\n                            columnNumber: 41\n                        }, this),\n                        breakType === 'affirmation' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AffirmationBreak, {}, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                            lineNumber: 574,\n                            columnNumber: 43\n                        }, this),\n                        breakType === 'rest' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(RestBreak, {}, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                            lineNumber: 575,\n                            columnNumber: 36\n                        }, this),\n                        !isBreakCompleted && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-8 text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"outline\",\n                                    onClick: onClose,\n                                    className: \"mr-4\",\n                                    children: \"Lewati Break\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                    lineNumber: 579,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    onClick: ()=>{\n                                        setIsBreakActive(false);\n                                        setIsBreakCompleted(true);\n                                    },\n                                    disabled: isBreakActive,\n                                    className: \"bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600\",\n                                    children: isBreakActive ? 'Sedang Break...' : 'Selesaikan Break'\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                    lineNumber: 586,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                            lineNumber: 578,\n                            columnNumber: 13\n                        }, this),\n                        isBreakCompleted && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-8 text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-4 p-3 bg-green-50 border border-green-200 rounded-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-green-800 font-medium\",\n                                        children: \"✓ Break selesai! Anda siap melanjutkan interview.\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                        lineNumber: 602,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                    lineNumber: 601,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    onClick: onBreakComplete,\n                                    className: \"bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600 text-lg px-8 py-3\",\n                                    children: \"Lanjutkan Interview\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                                    lineNumber: 604,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                            lineNumber: 600,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n                    lineNumber: 572,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n            lineNumber: 567,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Coding/Interview_ai_20250617/components/psychological-safety.tsx\",\n        lineNumber: 566,\n        columnNumber: 5\n    }, this);\n}\n_s(PsychologicalSafety, \"T80EamEx/gj3kiCRem+DYCNDD4Q=\");\n_c = PsychologicalSafety;\nvar _c;\n$RefreshReg$(_c, \"PsychologicalSafety\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/psychological-safety.tsx\n"));

/***/ })

});